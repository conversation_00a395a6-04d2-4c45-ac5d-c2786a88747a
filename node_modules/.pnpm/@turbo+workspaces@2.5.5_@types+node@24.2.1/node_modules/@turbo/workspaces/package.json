{"name": "@turbo/workspaces", "version": "2.5.5", "description": "Tools for working with package managers", "homepage": "https://turborepo.com", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vercel/turborepo", "directory": "packages/turbo-workspaces"}, "bugs": {"url": "https://github.com/vercel/turborepo/issues"}, "bin": "dist/cli.js", "module": "dist/index.mjs", "main": "dist/index.js", "types": "dist/index.d.ts", "dependencies": {"commander": "^10.0.0", "execa": "5.1.1", "fast-glob": "^3.2.12", "fs-extra": "^10.1.0", "gradient-string": "^2.0.0", "inquirer": "^8.0.0", "js-yaml": "^4.1.0", "ora": "4.1.1", "picocolors": "1.0.1", "semver": "7.6.2", "update-check": "^1.5.4"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/fs-extra": "^9.0.13", "@types/gradient-string": "^1.1.2", "@types/inquirer": "^7.3.1", "@types/js-yaml": "^4.0.5", "@types/node": "^18.17.2", "@types/semver": "7.5.8", "jest": "^29.7.0", "ts-jest": "^29.2.5", "tsup": "^5.10.3", "typescript": "5.5.4", "@turbo/eslint-config": "0.0.0", "@turbo/test-utils": "0.0.0", "@turbo/tsconfig": "0.0.0", "@turbo/utils": "0.0.0"}, "files": ["dist"], "publishConfig": {"access": "public"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "jest", "lint": "eslint src/", "check-types": "tsc --noEmit", "lint:prettier": "prettier -c . --cache --ignore-path=../../.prettierignore"}}