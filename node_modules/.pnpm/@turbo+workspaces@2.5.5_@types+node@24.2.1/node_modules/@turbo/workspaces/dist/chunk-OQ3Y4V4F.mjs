var sl=Object.create;var ei=Object.defineProperty;var il=Object.getOwnPropertyDescriptor;var nl=Object.getOwnPropertyNames;var ol=Object.getPrototypeOf,ul=Object.prototype.hasOwnProperty;var A=(r=>typeof require!="undefined"?require:typeof Proxy!="undefined"?new Proxy(r,{get:(e,t)=>(typeof require!="undefined"?require:e)[t]}):r)(function(r){if(typeof require!="undefined")return require.apply(this,arguments);throw new Error('Dynamic require of "'+r+'" is not supported')});var al=(r,e)=>()=>(r&&(e=r(r=0)),e);var y=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),cl=(r,e)=>{for(var t in e)ei(r,t,{get:e[t],enumerable:!0})},ll=(r,e,t,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of nl(e))!ul.call(r,i)&&i!==t&&ei(r,i,{get:()=>e[i],enumerable:!(s=il(e,i))||s.enumerable});return r};var Tr=(r,e,t)=>(t=r!=null?sl(ol(r)):{},ll(e||!r||!r.__esModule?ei(t,"default",{value:r,enumerable:!0}):t,r));var u=al(()=>{});var fo=y((Rm,Fe)=>{u();function si(r){return Fe.exports=si=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Fe.exports.__esModule=!0,Fe.exports.default=Fe.exports,si(r)}Fe.exports=si,Fe.exports.__esModule=!0,Fe.exports.default=Fe.exports});var Do=y((Bm,be)=>{u();var po=fo().default;function mo(){"use strict";be.exports=mo=function(){return e},be.exports.__esModule=!0,be.exports.default=be.exports;var r,e={},t=Object.prototype,s=t.hasOwnProperty,i=Object.defineProperty||function(E,f,D){E[f]=D.value},n=typeof Symbol=="function"?Symbol:{},o=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function c(E,f,D){return Object.defineProperty(E,f,{value:D,enumerable:!0,configurable:!0,writable:!0}),E[f]}try{c({},"")}catch{c=function(D,C,b){return D[C]=b}}function h(E,f,D,C){var b=f&&f.prototype instanceof O?f:O,F=Object.create(b.prototype),N=new Xs(C||[]);return i(F,"_invoke",{value:tl(E,D,N)}),F}function d(E,f,D){try{return{type:"normal",arg:E.call(f,D)}}catch(C){return{type:"throw",arg:C}}}e.wrap=h;var g="suspendedStart",w="suspendedYield",S="executing",P="completed",k={};function O(){}function ut(){}function me(){}var Ys={};c(Ys,o,function(){return this});var Ks=Object.getPrototypeOf,xr=Ks&&Ks(Ks(Qs([])));xr&&xr!==t&&s.call(xr,o)&&(Ys=xr);var kt=me.prototype=O.prototype=Object.create(Ys);function io(E){["next","throw","return"].forEach(function(f){c(E,f,function(D){return this._invoke(f,D)})})}function Or(E,f){function D(b,F,N,H){var J=d(E[b],E,F);if(J.type!=="throw"){var at=J.arg,qe=at.value;return qe&&po(qe)=="object"&&s.call(qe,"__await")?f.resolve(qe.__await).then(function(ct){D("next",ct,N,H)},function(ct){D("throw",ct,N,H)}):f.resolve(qe).then(function(ct){at.value=ct,N(at)},function(ct){return D("throw",ct,N,H)})}H(J.arg)}var C;i(this,"_invoke",{value:function(F,N){function H(){return new f(function(J,at){D(F,N,J,at)})}return C=C?C.then(H,H):H()}})}function tl(E,f,D){var C=g;return function(b,F){if(C===S)throw new Error("Generator is already running");if(C===P){if(b==="throw")throw F;return{value:r,done:!0}}for(D.method=b,D.arg=F;;){var N=D.delegate;if(N){var H=no(N,D);if(H){if(H===k)continue;return H}}if(D.method==="next")D.sent=D._sent=D.arg;else if(D.method==="throw"){if(C===g)throw C=P,D.arg;D.dispatchException(D.arg)}else D.method==="return"&&D.abrupt("return",D.arg);C=S;var J=d(E,f,D);if(J.type==="normal"){if(C=D.done?P:w,J.arg===k)continue;return{value:J.arg,done:D.done}}J.type==="throw"&&(C=P,D.method="throw",D.arg=J.arg)}}}function no(E,f){var D=f.method,C=E.iterator[D];if(C===r)return f.delegate=null,D==="throw"&&E.iterator.return&&(f.method="return",f.arg=r,no(E,f),f.method==="throw")||D!=="return"&&(f.method="throw",f.arg=new TypeError("The iterator does not provide a '"+D+"' method")),k;var b=d(C,E.iterator,f.arg);if(b.type==="throw")return f.method="throw",f.arg=b.arg,f.delegate=null,k;var F=b.arg;return F?F.done?(f[E.resultName]=F.value,f.next=E.nextLoc,f.method!=="return"&&(f.method="next",f.arg=r),f.delegate=null,k):F:(f.method="throw",f.arg=new TypeError("iterator result is not an object"),f.delegate=null,k)}function rl(E){var f={tryLoc:E[0]};1 in E&&(f.catchLoc=E[1]),2 in E&&(f.finallyLoc=E[2],f.afterLoc=E[3]),this.tryEntries.push(f)}function Zs(E){var f=E.completion||{};f.type="normal",delete f.arg,E.completion=f}function Xs(E){this.tryEntries=[{tryLoc:"root"}],E.forEach(rl,this),this.reset(!0)}function Qs(E){if(E||E===""){var f=E[o];if(f)return f.call(E);if(typeof E.next=="function")return E;if(!isNaN(E.length)){var D=-1,C=function b(){for(;++D<E.length;)if(s.call(E,D))return b.value=E[D],b.done=!1,b;return b.value=r,b.done=!0,b};return C.next=C}}throw new TypeError(po(E)+" is not iterable")}return ut.prototype=me,i(kt,"constructor",{value:me,configurable:!0}),i(me,"constructor",{value:ut,configurable:!0}),ut.displayName=c(me,l,"GeneratorFunction"),e.isGeneratorFunction=function(E){var f=typeof E=="function"&&E.constructor;return!!f&&(f===ut||(f.displayName||f.name)==="GeneratorFunction")},e.mark=function(E){return Object.setPrototypeOf?Object.setPrototypeOf(E,me):(E.__proto__=me,c(E,l,"GeneratorFunction")),E.prototype=Object.create(kt),E},e.awrap=function(E){return{__await:E}},io(Or.prototype),c(Or.prototype,a,function(){return this}),e.AsyncIterator=Or,e.async=function(E,f,D,C,b){b===void 0&&(b=Promise);var F=new Or(h(E,f,D,C),b);return e.isGeneratorFunction(f)?F:F.next().then(function(N){return N.done?N.value:F.next()})},io(kt),c(kt,l,"Generator"),c(kt,o,function(){return this}),c(kt,"toString",function(){return"[object Generator]"}),e.keys=function(E){var f=Object(E),D=[];for(var C in f)D.push(C);return D.reverse(),function b(){for(;D.length;){var F=D.pop();if(F in f)return b.value=F,b.done=!1,b}return b.done=!0,b}},e.values=Qs,Xs.prototype={constructor:Xs,reset:function(f){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(Zs),!f)for(var D in this)D.charAt(0)==="t"&&s.call(this,D)&&!isNaN(+D.slice(1))&&(this[D]=r)},stop:function(){this.done=!0;var f=this.tryEntries[0].completion;if(f.type==="throw")throw f.arg;return this.rval},dispatchException:function(f){if(this.done)throw f;var D=this;function C(at,qe){return N.type="throw",N.arg=f,D.next=at,qe&&(D.method="next",D.arg=r),!!qe}for(var b=this.tryEntries.length-1;b>=0;--b){var F=this.tryEntries[b],N=F.completion;if(F.tryLoc==="root")return C("end");if(F.tryLoc<=this.prev){var H=s.call(F,"catchLoc"),J=s.call(F,"finallyLoc");if(H&&J){if(this.prev<F.catchLoc)return C(F.catchLoc,!0);if(this.prev<F.finallyLoc)return C(F.finallyLoc)}else if(H){if(this.prev<F.catchLoc)return C(F.catchLoc,!0)}else{if(!J)throw new Error("try statement without catch or finally");if(this.prev<F.finallyLoc)return C(F.finallyLoc)}}}},abrupt:function(f,D){for(var C=this.tryEntries.length-1;C>=0;--C){var b=this.tryEntries[C];if(b.tryLoc<=this.prev&&s.call(b,"finallyLoc")&&this.prev<b.finallyLoc){var F=b;break}}F&&(f==="break"||f==="continue")&&F.tryLoc<=D&&D<=F.finallyLoc&&(F=null);var N=F?F.completion:{};return N.type=f,N.arg=D,F?(this.method="next",this.next=F.finallyLoc,k):this.complete(N)},complete:function(f,D){if(f.type==="throw")throw f.arg;return f.type==="break"||f.type==="continue"?this.next=f.arg:f.type==="return"?(this.rval=this.arg=f.arg,this.method="return",this.next="end"):f.type==="normal"&&D&&(this.next=D),k},finish:function(f){for(var D=this.tryEntries.length-1;D>=0;--D){var C=this.tryEntries[D];if(C.finallyLoc===f)return this.complete(C.completion,C.afterLoc),Zs(C),k}},catch:function(f){for(var D=this.tryEntries.length-1;D>=0;--D){var C=this.tryEntries[D];if(C.tryLoc===f){var b=C.completion;if(b.type==="throw"){var F=b.arg;Zs(C)}return F}}throw new Error("illegal catch attempt")},delegateYield:function(f,D,C){return this.delegate={iterator:Qs(f),resultName:D,nextLoc:C},this.method==="next"&&(this.arg=r),k}},e}be.exports=mo,be.exports.__esModule=!0,be.exports.default=be.exports});var Eo=y((vm,go)=>{u();var Lr=Do()();go.exports=Lr;try{regeneratorRuntime=Lr}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=Lr:Function("r","regeneratorRuntime = r")(Lr)}});var Ao=y((Xm,li)=>{"use strict";u();var yo=(r,...e)=>new Promise(t=>{t(r(...e))});li.exports=yo;li.exports.default=yo});var wo=y((Qm,hi)=>{"use strict";u();var Bl=Ao(),Co=r=>{if(!((Number.isInteger(r)||r===1/0)&&r>0))return Promise.reject(new TypeError("Expected `concurrency` to be a number from 1 and up"));let e=[],t=0,s=()=>{t--,e.length>0&&e.shift()()},i=(a,l,...c)=>{t++;let h=Bl(a,...c);l(h),h.then(s,s)},n=(a,l,...c)=>{t<r?i(a,l,...c):e.push(i.bind(null,a,l,...c))},o=(a,...l)=>new Promise(c=>n(a,c,...l));return Object.defineProperties(o,{activeCount:{get:()=>t},pendingCount:{get:()=>e.length},clearQueue:{value:()=>{e.length=0}}}),o};hi.exports=Co;hi.exports.default=Co});var So=y((ed,fi)=>{"use strict";u();var Fo=wo(),Mr=class extends Error{constructor(e){super(),this.value=e}},vl=async(r,e)=>e(await r),xl=async r=>{let e=await Promise.all(r);if(e[1]===!0)throw new Mr(e[0]);return!1},bo=async(r,e,t)=>{t={concurrency:1/0,preserveOrder:!0,...t};let s=Fo(t.concurrency),i=[...r].map(o=>[o,s(vl,o,e)]),n=Fo(t.preserveOrder?1:1/0);try{await Promise.all(i.map(o=>n(xl,o)))}catch(o){if(o instanceof Mr)return o.value;throw o}};fi.exports=bo;fi.exports.default=bo});var xo=y((td,pi)=>{"use strict";u();var ko=A("path"),Ir=A("fs"),{promisify:_o}=A("util"),Ol=So(),Tl=_o(Ir.stat),Pl=_o(Ir.lstat),Ro={directory:"isDirectory",file:"isFile"};function Bo({type:r}){if(!(r in Ro))throw new Error(`Invalid type specified: ${r}`)}var vo=(r,e)=>r===void 0||e[Ro[r]]();pi.exports=async(r,e)=>{e={cwd:process.cwd(),type:"file",allowSymlinks:!0,...e},Bo(e);let t=e.allowSymlinks?Tl:Pl;return Ol(r,async s=>{try{let i=await t(ko.resolve(e.cwd,s));return vo(e.type,i)}catch{return!1}},e)};pi.exports.sync=(r,e)=>{e={cwd:process.cwd(),allowSymlinks:!0,type:"file",...e},Bo(e);let t=e.allowSymlinks?Ir.statSync:Ir.lstatSync;for(let s of r)try{let i=t(ko.resolve(e.cwd,s));if(vo(e.type,i))return s}catch{}}});var To=y((rd,mi)=>{"use strict";u();var Oo=A("fs"),{promisify:Nl}=A("util"),Ll=Nl(Oo.access);mi.exports=async r=>{try{return await Ll(r),!0}catch{return!1}};mi.exports.sync=r=>{try{return Oo.accessSync(r),!0}catch{return!1}}});var No=y((sd,Bt)=>{"use strict";u();var $e=A("path"),jr=xo(),Po=To(),di=Symbol("findUp.stop");Bt.exports=async(r,e={})=>{let t=$e.resolve(e.cwd||""),{root:s}=$e.parse(t),i=[].concat(r),n=async o=>{if(typeof r!="function")return jr(i,o);let a=await r(o.cwd);return typeof a=="string"?jr([a],o):a};for(;;){let o=await n({...e,cwd:t});if(o===di)return;if(o)return $e.resolve(t,o);if(t===s)return;t=$e.dirname(t)}};Bt.exports.sync=(r,e={})=>{let t=$e.resolve(e.cwd||""),{root:s}=$e.parse(t),i=[].concat(r),n=o=>{if(typeof r!="function")return jr.sync(i,o);let a=r(o.cwd);return typeof a=="string"?jr.sync([a],o):a};for(;;){let o=n({...e,cwd:t});if(o===di)return;if(o)return $e.resolve(t,o);if(t===s)return;t=$e.dirname(t)}};Bt.exports.exists=Po;Bt.exports.sync.exists=Po.sync;Bt.exports.stop=di});var vt=y((Id,jo)=>{"use strict";u();var Io=new Map([["C","cwd"],["f","file"],["z","gzip"],["P","preservePaths"],["U","unlink"],["strip-components","strip"],["stripComponents","strip"],["keep-newer","newer"],["keepNewer","newer"],["keep-newer-files","newer"],["keepNewerFiles","newer"],["k","keep"],["keep-existing","keep"],["keepExisting","keep"],["m","noMtime"],["no-mtime","noMtime"],["p","preserveOwner"],["L","follow"],["h","follow"]]);jo.exports=r=>r?Object.keys(r).map(e=>[Io.has(e)?Io.get(e):e,r[e]]).reduce((e,t)=>(e[t[0]]=t[1],e),Object.create(null)):{}});var Gr=y((jd,Vo)=>{"use strict";u();var qo=typeof process=="object"&&process?process:{stdout:null,stderr:null},Ul=A("events"),Uo=A("stream"),$o=A("string_decoder").StringDecoder,ke=Symbol("EOF"),_e=Symbol("maybeEmitEnd"),ze=Symbol("emittedEnd"),qr=Symbol("emittingEnd"),lr=Symbol("emittedError"),Ur=Symbol("closed"),zo=Symbol("read"),$r=Symbol("flush"),Wo=Symbol("flushChunk"),V=Symbol("encoding"),Re=Symbol("decoder"),zr=Symbol("flowing"),hr=Symbol("paused"),xt=Symbol("resume"),L=Symbol("buffer"),de=Symbol("pipes"),j=Symbol("bufferLength"),Di=Symbol("bufferPush"),gi=Symbol("bufferShift"),U=Symbol("objectMode"),$=Symbol("destroyed"),Ei=Symbol("emitData"),Go=Symbol("emitEnd"),yi=Symbol("emitEnd2"),Be=Symbol("async"),fr=r=>Promise.resolve().then(r),Ho=global._MP_NO_ITERATOR_SYMBOLS_!=="1",$l=Ho&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),zl=Ho&&Symbol.iterator||Symbol("iterator not implemented"),Wl=r=>r==="end"||r==="finish"||r==="prefinish",Gl=r=>r instanceof ArrayBuffer||typeof r=="object"&&r.constructor&&r.constructor.name==="ArrayBuffer"&&r.byteLength>=0,Hl=r=>!Buffer.isBuffer(r)&&ArrayBuffer.isView(r),Wr=class{constructor(e,t,s){this.src=e,this.dest=t,this.opts=s,this.ondrain=()=>e[xt](),t.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}},Ai=class extends Wr{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(e,t,s){super(e,t,s),this.proxyErrors=i=>t.emit("error",i),e.on("error",this.proxyErrors)}};Vo.exports=class Jo extends Uo{constructor(e){super(),this[zr]=!1,this[hr]=!1,this[de]=[],this[L]=[],this[U]=e&&e.objectMode||!1,this[U]?this[V]=null:this[V]=e&&e.encoding||null,this[V]==="buffer"&&(this[V]=null),this[Be]=e&&!!e.async||!1,this[Re]=this[V]?new $o(this[V]):null,this[ke]=!1,this[ze]=!1,this[qr]=!1,this[Ur]=!1,this[lr]=null,this.writable=!0,this.readable=!0,this[j]=0,this[$]=!1,e&&e.debugExposeBuffer===!0&&Object.defineProperty(this,"buffer",{get:()=>this[L]}),e&&e.debugExposePipes===!0&&Object.defineProperty(this,"pipes",{get:()=>this[de]})}get bufferLength(){return this[j]}get encoding(){return this[V]}set encoding(e){if(this[U])throw new Error("cannot set encoding in objectMode");if(this[V]&&e!==this[V]&&(this[Re]&&this[Re].lastNeed||this[j]))throw new Error("cannot change encoding");this[V]!==e&&(this[Re]=e?new $o(e):null,this[L].length&&(this[L]=this[L].map(t=>this[Re].write(t)))),this[V]=e}setEncoding(e){this.encoding=e}get objectMode(){return this[U]}set objectMode(e){this[U]=this[U]||!!e}get async(){return this[Be]}set async(e){this[Be]=this[Be]||!!e}write(e,t,s){if(this[ke])throw new Error("write after end");if(this[$])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;typeof t=="function"&&(s=t,t="utf8"),t||(t="utf8");let i=this[Be]?fr:n=>n();return!this[U]&&!Buffer.isBuffer(e)&&(Hl(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):Gl(e)?e=Buffer.from(e):typeof e!="string"&&(this.objectMode=!0)),this[U]?(this.flowing&&this[j]!==0&&this[$r](!0),this.flowing?this.emit("data",e):this[Di](e),this[j]!==0&&this.emit("readable"),s&&i(s),this.flowing):e.length?(typeof e=="string"&&!(t===this[V]&&!this[Re].lastNeed)&&(e=Buffer.from(e,t)),Buffer.isBuffer(e)&&this[V]&&(e=this[Re].write(e)),this.flowing&&this[j]!==0&&this[$r](!0),this.flowing?this.emit("data",e):this[Di](e),this[j]!==0&&this.emit("readable"),s&&i(s),this.flowing):(this[j]!==0&&this.emit("readable"),s&&i(s),this.flowing)}read(e){if(this[$])return null;if(this[j]===0||e===0||e>this[j])return this[_e](),null;this[U]&&(e=null),this[L].length>1&&!this[U]&&(this.encoding?this[L]=[this[L].join("")]:this[L]=[Buffer.concat(this[L],this[j])]);let t=this[zo](e||null,this[L][0]);return this[_e](),t}[zo](e,t){return e===t.length||e===null?this[gi]():(this[L][0]=t.slice(e),t=t.slice(0,e),this[j]-=e),this.emit("data",t),!this[L].length&&!this[ke]&&this.emit("drain"),t}end(e,t,s){return typeof e=="function"&&(s=e,e=null),typeof t=="function"&&(s=t,t="utf8"),e&&this.write(e,t),s&&this.once("end",s),this[ke]=!0,this.writable=!1,(this.flowing||!this[hr])&&this[_e](),this}[xt](){this[$]||(this[hr]=!1,this[zr]=!0,this.emit("resume"),this[L].length?this[$r]():this[ke]?this[_e]():this.emit("drain"))}resume(){return this[xt]()}pause(){this[zr]=!1,this[hr]=!0}get destroyed(){return this[$]}get flowing(){return this[zr]}get paused(){return this[hr]}[Di](e){this[U]?this[j]+=1:this[j]+=e.length,this[L].push(e)}[gi](){return this[L].length&&(this[U]?this[j]-=1:this[j]-=this[L][0].length),this[L].shift()}[$r](e){do;while(this[Wo](this[gi]()));!e&&!this[L].length&&!this[ke]&&this.emit("drain")}[Wo](e){return e?(this.emit("data",e),this.flowing):!1}pipe(e,t){if(this[$])return;let s=this[ze];return t=t||{},e===qo.stdout||e===qo.stderr?t.end=!1:t.end=t.end!==!1,t.proxyErrors=!!t.proxyErrors,s?t.end&&e.end():(this[de].push(t.proxyErrors?new Ai(this,e,t):new Wr(this,e,t)),this[Be]?fr(()=>this[xt]()):this[xt]()),e}unpipe(e){let t=this[de].find(s=>s.dest===e);t&&(this[de].splice(this[de].indexOf(t),1),t.unpipe())}addListener(e,t){return this.on(e,t)}on(e,t){let s=super.on(e,t);return e==="data"&&!this[de].length&&!this.flowing?this[xt]():e==="readable"&&this[j]!==0?super.emit("readable"):Wl(e)&&this[ze]?(super.emit(e),this.removeAllListeners(e)):e==="error"&&this[lr]&&(this[Be]?fr(()=>t.call(this,this[lr])):t.call(this,this[lr])),s}get emittedEnd(){return this[ze]}[_e](){!this[qr]&&!this[ze]&&!this[$]&&this[L].length===0&&this[ke]&&(this[qr]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[Ur]&&this.emit("close"),this[qr]=!1)}emit(e,t,...s){if(e!=="error"&&e!=="close"&&e!==$&&this[$])return;if(e==="data")return t?this[Be]?fr(()=>this[Ei](t)):this[Ei](t):!1;if(e==="end")return this[Go]();if(e==="close"){if(this[Ur]=!0,!this[ze]&&!this[$])return;let n=super.emit("close");return this.removeAllListeners("close"),n}else if(e==="error"){this[lr]=t;let n=super.emit("error",t);return this[_e](),n}else if(e==="resume"){let n=super.emit("resume");return this[_e](),n}else if(e==="finish"||e==="prefinish"){let n=super.emit(e);return this.removeAllListeners(e),n}let i=super.emit(e,t,...s);return this[_e](),i}[Ei](e){for(let s of this[de])s.dest.write(e)===!1&&this.pause();let t=super.emit("data",e);return this[_e](),t}[Go](){this[ze]||(this[ze]=!0,this.readable=!1,this[Be]?fr(()=>this[yi]()):this[yi]())}[yi](){if(this[Re]){let t=this[Re].end();if(t){for(let s of this[de])s.dest.write(t);super.emit("data",t)}}for(let t of this[de])t.end();let e=super.emit("end");return this.removeAllListeners("end"),e}collect(){let e=[];this[U]||(e.dataLength=0);let t=this.promise();return this.on("data",s=>{e.push(s),this[U]||(e.dataLength+=s.length)}),t.then(()=>e)}concat(){return this[U]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then(e=>this[U]?Promise.reject(new Error("cannot concat in objectMode")):this[V]?e.join(""):Buffer.concat(e,e.dataLength))}promise(){return new Promise((e,t)=>{this.on($,()=>t(new Error("stream destroyed"))),this.on("error",s=>t(s)),this.on("end",()=>e())})}[$l](){return{next:()=>{let t=this.read();if(t!==null)return Promise.resolve({done:!1,value:t});if(this[ke])return Promise.resolve({done:!0});let s=null,i=null,n=c=>{this.removeListener("data",o),this.removeListener("end",a),i(c)},o=c=>{this.removeListener("error",n),this.removeListener("end",a),this.pause(),s({value:c,done:!!this[ke]})},a=()=>{this.removeListener("error",n),this.removeListener("data",o),s({done:!0})},l=()=>n(new Error("stream destroyed"));return new Promise((c,h)=>{i=h,s=c,this.once($,l),this.once("error",n),this.once("end",a),this.once("data",o)})}}}[zl](){return{next:()=>{let t=this.read();return{value:t,done:t===null}}}}destroy(e){return this[$]?(e?this.emit("error",e):this.emit($),this):(this[$]=!0,this[L].length=0,this[j]=0,typeof this.close=="function"&&!this[Ur]&&this.close(),e?this.emit("error",e):this.emit($),this)}static isStream(e){return!!e&&(e instanceof Jo||e instanceof Uo||e instanceof Ul&&(typeof e.pipe=="function"||typeof e.write=="function"&&typeof e.end=="function"))}}});var Ko=y((qd,Yo)=>{u();var Jl=A("zlib").constants||{ZLIB_VERNUM:4736};Yo.exports=Object.freeze(Object.assign(Object.create(null),{Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_VERSION_ERROR:-6,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,DEFLATE:1,INFLATE:2,GZIP:3,GUNZIP:4,DEFLATERAW:5,INFLATERAW:6,UNZIP:7,BROTLI_DECODE:8,BROTLI_ENCODE:9,Z_MIN_WINDOWBITS:8,Z_MAX_WINDOWBITS:15,Z_DEFAULT_WINDOWBITS:15,Z_MIN_CHUNK:64,Z_MAX_CHUNK:1/0,Z_DEFAULT_CHUNK:16384,Z_MIN_MEMLEVEL:1,Z_MAX_MEMLEVEL:9,Z_DEFAULT_MEMLEVEL:8,Z_MIN_LEVEL:-1,Z_MAX_LEVEL:9,Z_DEFAULT_LEVEL:-1,BROTLI_OPERATION_PROCESS:0,BROTLI_OPERATION_FLUSH:1,BROTLI_OPERATION_FINISH:2,BROTLI_OPERATION_EMIT_METADATA:3,BROTLI_MODE_GENERIC:0,BROTLI_MODE_TEXT:1,BROTLI_MODE_FONT:2,BROTLI_DEFAULT_MODE:0,BROTLI_MIN_QUALITY:0,BROTLI_MAX_QUALITY:11,BROTLI_DEFAULT_QUALITY:11,BROTLI_MIN_WINDOW_BITS:10,BROTLI_MAX_WINDOW_BITS:24,BROTLI_LARGE_MAX_WINDOW_BITS:30,BROTLI_DEFAULT_WINDOW:22,BROTLI_MIN_INPUT_BLOCK_BITS:16,BROTLI_MAX_INPUT_BLOCK_BITS:24,BROTLI_PARAM_MODE:0,BROTLI_PARAM_QUALITY:1,BROTLI_PARAM_LGWIN:2,BROTLI_PARAM_LGBLOCK:3,BROTLI_PARAM_DISABLE_LITERAL_CONTEXT_MODELING:4,BROTLI_PARAM_SIZE_HINT:5,BROTLI_PARAM_LARGE_WINDOW:6,BROTLI_PARAM_NPOSTFIX:7,BROTLI_PARAM_NDIRECT:8,BROTLI_DECODER_RESULT_ERROR:0,BROTLI_DECODER_RESULT_SUCCESS:1,BROTLI_DECODER_RESULT_NEEDS_MORE_INPUT:2,BROTLI_DECODER_RESULT_NEEDS_MORE_OUTPUT:3,BROTLI_DECODER_PARAM_DISABLE_RING_BUFFER_REALLOCATION:0,BROTLI_DECODER_PARAM_LARGE_WINDOW:1,BROTLI_DECODER_NO_ERROR:0,BROTLI_DECODER_SUCCESS:1,BROTLI_DECODER_NEEDS_MORE_INPUT:2,BROTLI_DECODER_NEEDS_MORE_OUTPUT:3,BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_NIBBLE:-1,BROTLI_DECODER_ERROR_FORMAT_RESERVED:-2,BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_META_NIBBLE:-3,BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_ALPHABET:-4,BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_SAME:-5,BROTLI_DECODER_ERROR_FORMAT_CL_SPACE:-6,BROTLI_DECODER_ERROR_FORMAT_HUFFMAN_SPACE:-7,BROTLI_DECODER_ERROR_FORMAT_CONTEXT_MAP_REPEAT:-8,BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_1:-9,BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_2:-10,BROTLI_DECODER_ERROR_FORMAT_TRANSFORM:-11,BROTLI_DECODER_ERROR_FORMAT_DICTIONARY:-12,BROTLI_DECODER_ERROR_FORMAT_WINDOW_BITS:-13,BROTLI_DECODER_ERROR_FORMAT_PADDING_1:-14,BROTLI_DECODER_ERROR_FORMAT_PADDING_2:-15,BROTLI_DECODER_ERROR_FORMAT_DISTANCE:-16,BROTLI_DECODER_ERROR_DICTIONARY_NOT_SET:-19,BROTLI_DECODER_ERROR_INVALID_ARGUMENTS:-20,BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MODES:-21,BROTLI_DECODER_ERROR_ALLOC_TREE_GROUPS:-22,BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MAP:-25,BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_1:-26,BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_2:-27,BROTLI_DECODER_ERROR_ALLOC_BLOCK_TYPE_TREES:-30,BROTLI_DECODER_ERROR_UNREACHABLE:-31},Jl))});var ki=y((Ud,nu)=>{"use strict";u();var Zo=typeof process=="object"&&process?process:{stdout:null,stderr:null},Vl=A("events"),Xo=A("stream"),Qo=A("string_decoder").StringDecoder,ve=Symbol("EOF"),xe=Symbol("maybeEmitEnd"),We=Symbol("emittedEnd"),Hr=Symbol("emittingEnd"),pr=Symbol("emittedError"),Jr=Symbol("closed"),eu=Symbol("read"),Vr=Symbol("flush"),tu=Symbol("flushChunk"),Y=Symbol("encoding"),Oe=Symbol("decoder"),Yr=Symbol("flowing"),mr=Symbol("paused"),Ot=Symbol("resume"),q=Symbol("bufferLength"),Ci=Symbol("bufferPush"),wi=Symbol("bufferShift"),z=Symbol("objectMode"),W=Symbol("destroyed"),Fi=Symbol("emitData"),ru=Symbol("emitEnd"),bi=Symbol("emitEnd2"),Te=Symbol("async"),dr=r=>Promise.resolve().then(r),su=global._MP_NO_ITERATOR_SYMBOLS_!=="1",Yl=su&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),Kl=su&&Symbol.iterator||Symbol("iterator not implemented"),Zl=r=>r==="end"||r==="finish"||r==="prefinish",Xl=r=>r instanceof ArrayBuffer||typeof r=="object"&&r.constructor&&r.constructor.name==="ArrayBuffer"&&r.byteLength>=0,Ql=r=>!Buffer.isBuffer(r)&&ArrayBuffer.isView(r),Kr=class{constructor(e,t,s){this.src=e,this.dest=t,this.opts=s,this.ondrain=()=>e[Ot](),t.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}},Si=class extends Kr{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(e,t,s){super(e,t,s),this.proxyErrors=i=>t.emit("error",i),e.on("error",this.proxyErrors)}};nu.exports=class iu extends Xo{constructor(e){super(),this[Yr]=!1,this[mr]=!1,this.pipes=[],this.buffer=[],this[z]=e&&e.objectMode||!1,this[z]?this[Y]=null:this[Y]=e&&e.encoding||null,this[Y]==="buffer"&&(this[Y]=null),this[Te]=e&&!!e.async||!1,this[Oe]=this[Y]?new Qo(this[Y]):null,this[ve]=!1,this[We]=!1,this[Hr]=!1,this[Jr]=!1,this[pr]=null,this.writable=!0,this.readable=!0,this[q]=0,this[W]=!1}get bufferLength(){return this[q]}get encoding(){return this[Y]}set encoding(e){if(this[z])throw new Error("cannot set encoding in objectMode");if(this[Y]&&e!==this[Y]&&(this[Oe]&&this[Oe].lastNeed||this[q]))throw new Error("cannot change encoding");this[Y]!==e&&(this[Oe]=e?new Qo(e):null,this.buffer.length&&(this.buffer=this.buffer.map(t=>this[Oe].write(t)))),this[Y]=e}setEncoding(e){this.encoding=e}get objectMode(){return this[z]}set objectMode(e){this[z]=this[z]||!!e}get async(){return this[Te]}set async(e){this[Te]=this[Te]||!!e}write(e,t,s){if(this[ve])throw new Error("write after end");if(this[W])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;typeof t=="function"&&(s=t,t="utf8"),t||(t="utf8");let i=this[Te]?dr:n=>n();return!this[z]&&!Buffer.isBuffer(e)&&(Ql(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):Xl(e)?e=Buffer.from(e):typeof e!="string"&&(this.objectMode=!0)),this[z]?(this.flowing&&this[q]!==0&&this[Vr](!0),this.flowing?this.emit("data",e):this[Ci](e),this[q]!==0&&this.emit("readable"),s&&i(s),this.flowing):e.length?(typeof e=="string"&&!(t===this[Y]&&!this[Oe].lastNeed)&&(e=Buffer.from(e,t)),Buffer.isBuffer(e)&&this[Y]&&(e=this[Oe].write(e)),this.flowing&&this[q]!==0&&this[Vr](!0),this.flowing?this.emit("data",e):this[Ci](e),this[q]!==0&&this.emit("readable"),s&&i(s),this.flowing):(this[q]!==0&&this.emit("readable"),s&&i(s),this.flowing)}read(e){if(this[W])return null;if(this[q]===0||e===0||e>this[q])return this[xe](),null;this[z]&&(e=null),this.buffer.length>1&&!this[z]&&(this.encoding?this.buffer=[this.buffer.join("")]:this.buffer=[Buffer.concat(this.buffer,this[q])]);let t=this[eu](e||null,this.buffer[0]);return this[xe](),t}[eu](e,t){return e===t.length||e===null?this[wi]():(this.buffer[0]=t.slice(e),t=t.slice(0,e),this[q]-=e),this.emit("data",t),!this.buffer.length&&!this[ve]&&this.emit("drain"),t}end(e,t,s){return typeof e=="function"&&(s=e,e=null),typeof t=="function"&&(s=t,t="utf8"),e&&this.write(e,t),s&&this.once("end",s),this[ve]=!0,this.writable=!1,(this.flowing||!this[mr])&&this[xe](),this}[Ot](){this[W]||(this[mr]=!1,this[Yr]=!0,this.emit("resume"),this.buffer.length?this[Vr]():this[ve]?this[xe]():this.emit("drain"))}resume(){return this[Ot]()}pause(){this[Yr]=!1,this[mr]=!0}get destroyed(){return this[W]}get flowing(){return this[Yr]}get paused(){return this[mr]}[Ci](e){this[z]?this[q]+=1:this[q]+=e.length,this.buffer.push(e)}[wi](){return this.buffer.length&&(this[z]?this[q]-=1:this[q]-=this.buffer[0].length),this.buffer.shift()}[Vr](e){do;while(this[tu](this[wi]()));!e&&!this.buffer.length&&!this[ve]&&this.emit("drain")}[tu](e){return e?(this.emit("data",e),this.flowing):!1}pipe(e,t){if(this[W])return;let s=this[We];return t=t||{},e===Zo.stdout||e===Zo.stderr?t.end=!1:t.end=t.end!==!1,t.proxyErrors=!!t.proxyErrors,s?t.end&&e.end():(this.pipes.push(t.proxyErrors?new Si(this,e,t):new Kr(this,e,t)),this[Te]?dr(()=>this[Ot]()):this[Ot]()),e}unpipe(e){let t=this.pipes.find(s=>s.dest===e);t&&(this.pipes.splice(this.pipes.indexOf(t),1),t.unpipe())}addListener(e,t){return this.on(e,t)}on(e,t){let s=super.on(e,t);return e==="data"&&!this.pipes.length&&!this.flowing?this[Ot]():e==="readable"&&this[q]!==0?super.emit("readable"):Zl(e)&&this[We]?(super.emit(e),this.removeAllListeners(e)):e==="error"&&this[pr]&&(this[Te]?dr(()=>t.call(this,this[pr])):t.call(this,this[pr])),s}get emittedEnd(){return this[We]}[xe](){!this[Hr]&&!this[We]&&!this[W]&&this.buffer.length===0&&this[ve]&&(this[Hr]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[Jr]&&this.emit("close"),this[Hr]=!1)}emit(e,t,...s){if(e!=="error"&&e!=="close"&&e!==W&&this[W])return;if(e==="data")return t?this[Te]?dr(()=>this[Fi](t)):this[Fi](t):!1;if(e==="end")return this[ru]();if(e==="close"){if(this[Jr]=!0,!this[We]&&!this[W])return;let n=super.emit("close");return this.removeAllListeners("close"),n}else if(e==="error"){this[pr]=t;let n=super.emit("error",t);return this[xe](),n}else if(e==="resume"){let n=super.emit("resume");return this[xe](),n}else if(e==="finish"||e==="prefinish"){let n=super.emit(e);return this.removeAllListeners(e),n}let i=super.emit(e,t,...s);return this[xe](),i}[Fi](e){for(let s of this.pipes)s.dest.write(e)===!1&&this.pause();let t=super.emit("data",e);return this[xe](),t}[ru](){this[We]||(this[We]=!0,this.readable=!1,this[Te]?dr(()=>this[bi]()):this[bi]())}[bi](){if(this[Oe]){let t=this[Oe].end();if(t){for(let s of this.pipes)s.dest.write(t);super.emit("data",t)}}for(let t of this.pipes)t.end();let e=super.emit("end");return this.removeAllListeners("end"),e}collect(){let e=[];this[z]||(e.dataLength=0);let t=this.promise();return this.on("data",s=>{e.push(s),this[z]||(e.dataLength+=s.length)}),t.then(()=>e)}concat(){return this[z]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then(e=>this[z]?Promise.reject(new Error("cannot concat in objectMode")):this[Y]?e.join(""):Buffer.concat(e,e.dataLength))}promise(){return new Promise((e,t)=>{this.on(W,()=>t(new Error("stream destroyed"))),this.on("error",s=>t(s)),this.on("end",()=>e())})}[Yl](){return{next:()=>{let t=this.read();if(t!==null)return Promise.resolve({done:!1,value:t});if(this[ve])return Promise.resolve({done:!0});let s=null,i=null,n=c=>{this.removeListener("data",o),this.removeListener("end",a),i(c)},o=c=>{this.removeListener("error",n),this.removeListener("end",a),this.pause(),s({value:c,done:!!this[ve]})},a=()=>{this.removeListener("error",n),this.removeListener("data",o),s({done:!0})},l=()=>n(new Error("stream destroyed"));return new Promise((c,h)=>{i=h,s=c,this.once(W,l),this.once("error",n),this.once("end",a),this.once("data",o)})}}}[Kl](){return{next:()=>{let t=this.read();return{value:t,done:t===null}}}}destroy(e){return this[W]?(e?this.emit("error",e):this.emit(W),this):(this[W]=!0,this.buffer.length=0,this[q]=0,typeof this.close=="function"&&!this[Jr]&&this.close(),e?this.emit("error",e):this.emit(W),this)}static isStream(e){return!!e&&(e instanceof iu||e instanceof Xo||e instanceof Vl&&(typeof e.pipe=="function"||typeof e.write=="function"&&typeof e.end=="function"))}}});var $i=y(X=>{"use strict";u();var xi=A("assert"),Ge=A("buffer").Buffer,au=A("zlib"),ht=X.constants=Ko(),eh=ki(),ou=Ge.concat,ft=Symbol("_superWrite"),Pt=class extends Error{constructor(e){super("zlib: "+e.message),this.code=e.code,this.errno=e.errno,this.code||(this.code="ZLIB_ERROR"),this.message="zlib: "+e.message,Error.captureStackTrace(this,this.constructor)}get name(){return"ZlibError"}},th=Symbol("opts"),Dr=Symbol("flushFlag"),uu=Symbol("finishFlushFlag"),Ui=Symbol("fullFlushFlag"),x=Symbol("handle"),Zr=Symbol("onError"),Tt=Symbol("sawError"),_i=Symbol("level"),Ri=Symbol("strategy"),Bi=Symbol("ended"),$d=Symbol("_defaultFullFlush"),Xr=class extends eh{constructor(e,t){if(!e||typeof e!="object")throw new TypeError("invalid options for ZlibBase constructor");super(e),this[Tt]=!1,this[Bi]=!1,this[th]=e,this[Dr]=e.flush,this[uu]=e.finishFlush;try{this[x]=new au[t](e)}catch(s){throw new Pt(s)}this[Zr]=s=>{this[Tt]||(this[Tt]=!0,this.close(),this.emit("error",s))},this[x].on("error",s=>this[Zr](new Pt(s))),this.once("end",()=>this.close)}close(){this[x]&&(this[x].close(),this[x]=null,this.emit("close"))}reset(){if(!this[Tt])return xi(this[x],"zlib binding closed"),this[x].reset()}flush(e){this.ended||(typeof e!="number"&&(e=this[Ui]),this.write(Object.assign(Ge.alloc(0),{[Dr]:e})))}end(e,t,s){return e&&this.write(e,t),this.flush(this[uu]),this[Bi]=!0,super.end(null,null,s)}get ended(){return this[Bi]}write(e,t,s){if(typeof t=="function"&&(s=t,t="utf8"),typeof e=="string"&&(e=Ge.from(e,t)),this[Tt])return;xi(this[x],"zlib binding closed");let i=this[x]._handle,n=i.close;i.close=()=>{};let o=this[x].close;this[x].close=()=>{},Ge.concat=c=>c;let a;try{let c=typeof e[Dr]=="number"?e[Dr]:this[Dr];a=this[x]._processChunk(e,c),Ge.concat=ou}catch(c){Ge.concat=ou,this[Zr](new Pt(c))}finally{this[x]&&(this[x]._handle=i,i.close=n,this[x].close=o,this[x].removeAllListeners("error"))}this[x]&&this[x].on("error",c=>this[Zr](new Pt(c)));let l;if(a)if(Array.isArray(a)&&a.length>0){l=this[ft](Ge.from(a[0]));for(let c=1;c<a.length;c++)l=this[ft](a[c])}else l=this[ft](Ge.from(a));return s&&s(),l}[ft](e){return super.write(e)}},Pe=class extends Xr{constructor(e,t){e=e||{},e.flush=e.flush||ht.Z_NO_FLUSH,e.finishFlush=e.finishFlush||ht.Z_FINISH,super(e,t),this[Ui]=ht.Z_FULL_FLUSH,this[_i]=e.level,this[Ri]=e.strategy}params(e,t){if(!this[Tt]){if(!this[x])throw new Error("cannot switch params when binding is closed");if(!this[x].params)throw new Error("not supported in this implementation");if(this[_i]!==e||this[Ri]!==t){this.flush(ht.Z_SYNC_FLUSH),xi(this[x],"zlib binding closed");let s=this[x].flush;this[x].flush=(i,n)=>{this.flush(i),n()};try{this[x].params(e,t)}finally{this[x].flush=s}this[x]&&(this[_i]=e,this[Ri]=t)}}}},Oi=class extends Pe{constructor(e){super(e,"Deflate")}},Ti=class extends Pe{constructor(e){super(e,"Inflate")}},vi=Symbol("_portable"),Pi=class extends Pe{constructor(e){super(e,"Gzip"),this[vi]=e&&!!e.portable}[ft](e){return this[vi]?(this[vi]=!1,e[9]=255,super[ft](e)):super[ft](e)}},Ni=class extends Pe{constructor(e){super(e,"Gunzip")}},Li=class extends Pe{constructor(e){super(e,"DeflateRaw")}},Mi=class extends Pe{constructor(e){super(e,"InflateRaw")}},Ii=class extends Pe{constructor(e){super(e,"Unzip")}},Qr=class extends Xr{constructor(e,t){e=e||{},e.flush=e.flush||ht.BROTLI_OPERATION_PROCESS,e.finishFlush=e.finishFlush||ht.BROTLI_OPERATION_FINISH,super(e,t),this[Ui]=ht.BROTLI_OPERATION_FLUSH}},ji=class extends Qr{constructor(e){super(e,"BrotliCompress")}},qi=class extends Qr{constructor(e){super(e,"BrotliDecompress")}};X.Deflate=Oi;X.Inflate=Ti;X.Gzip=Pi;X.Gunzip=Ni;X.DeflateRaw=Li;X.InflateRaw=Mi;X.Unzip=Ii;typeof au.BrotliCompress=="function"?(X.BrotliCompress=ji,X.BrotliDecompress=qi):X.BrotliCompress=X.BrotliDecompress=class{constructor(){throw new Error("Brotli is not supported in this version of Node.js")}}});var Nt=y((Gd,cu)=>{u();var rh=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform;cu.exports=rh!=="win32"?r=>r:r=>r&&r.replace(/\\/g,"/")});var es=y((Jd,lu)=>{"use strict";u();var sh=Gr(),zi=Nt(),Wi=Symbol("slurp");lu.exports=class extends sh{constructor(e,t,s){switch(super(),this.pause(),this.extended=t,this.globalExtended=s,this.header=e,this.startBlockSize=512*Math.ceil(e.size/512),this.blockRemain=this.startBlockSize,this.remain=e.size,this.type=e.type,this.meta=!1,this.ignore=!1,this.type){case"File":case"OldFile":case"Link":case"SymbolicLink":case"CharacterDevice":case"BlockDevice":case"Directory":case"FIFO":case"ContiguousFile":case"GNUDumpDir":break;case"NextFileHasLongLinkpath":case"NextFileHasLongPath":case"OldGnuLongPath":case"GlobalExtendedHeader":case"ExtendedHeader":case"OldExtendedHeader":this.meta=!0;break;default:this.ignore=!0}this.path=zi(e.path),this.mode=e.mode,this.mode&&(this.mode=this.mode&4095),this.uid=e.uid,this.gid=e.gid,this.uname=e.uname,this.gname=e.gname,this.size=e.size,this.mtime=e.mtime,this.atime=e.atime,this.ctime=e.ctime,this.linkpath=zi(e.linkpath),this.uname=e.uname,this.gname=e.gname,t&&this[Wi](t),s&&this[Wi](s,!0)}write(e){let t=e.length;if(t>this.blockRemain)throw new Error("writing more to entry than is appropriate");let s=this.remain,i=this.blockRemain;return this.remain=Math.max(0,s-t),this.blockRemain=Math.max(0,i-t),this.ignore?!0:s>=t?super.write(e):super.write(e.slice(0,s))}[Wi](e,t){for(let s in e)e[s]!==null&&e[s]!==void 0&&!(t&&s==="path")&&(this[s]=s==="path"||s==="linkpath"?zi(e[s]):e[s])}}});var Gi=y(ts=>{"use strict";u();ts.name=new Map([["0","File"],["","OldFile"],["1","Link"],["2","SymbolicLink"],["3","CharacterDevice"],["4","BlockDevice"],["5","Directory"],["6","FIFO"],["7","ContiguousFile"],["g","GlobalExtendedHeader"],["x","ExtendedHeader"],["A","SolarisACL"],["D","GNUDumpDir"],["I","Inode"],["K","NextFileHasLongLinkpath"],["L","NextFileHasLongPath"],["M","ContinuationFile"],["N","OldGnuLongPath"],["S","SparseFile"],["V","TapeVolumeHeader"],["X","OldExtendedHeader"]]);ts.code=new Map(Array.from(ts.name).map(r=>[r[1],r[0]]))});var mu=y((Yd,pu)=>{"use strict";u();var ih=(r,e)=>{if(Number.isSafeInteger(r))r<0?oh(r,e):nh(r,e);else throw Error("cannot encode number outside of javascript safe integer range");return e},nh=(r,e)=>{e[0]=128;for(var t=e.length;t>1;t--)e[t-1]=r&255,r=Math.floor(r/256)},oh=(r,e)=>{e[0]=255;var t=!1;r=r*-1;for(var s=e.length;s>1;s--){var i=r&255;r=Math.floor(r/256),t?e[s-1]=hu(i):i===0?e[s-1]=0:(t=!0,e[s-1]=fu(i))}},uh=r=>{let e=r[0],t=e===128?ch(r.slice(1,r.length)):e===255?ah(r):null;if(t===null)throw Error("invalid base256 encoding");if(!Number.isSafeInteger(t))throw Error("parsed number outside of javascript safe integer range");return t},ah=r=>{for(var e=r.length,t=0,s=!1,i=e-1;i>-1;i--){var n=r[i],o;s?o=hu(n):n===0?o=n:(s=!0,o=fu(n)),o!==0&&(t-=o*Math.pow(256,e-i-1))}return t},ch=r=>{for(var e=r.length,t=0,s=e-1;s>-1;s--){var i=r[s];i!==0&&(t+=i*Math.pow(256,e-s-1))}return t},hu=r=>(255^r)&255,fu=r=>(255^r)+1&255;pu.exports={encode:ih,parse:uh}});var Mt=y((Kd,Du)=>{"use strict";u();var Hi=Gi(),Lt=A("path").posix,du=mu(),Ji=Symbol("slurp"),Q=Symbol("type"),Ki=class{constructor(e,t,s,i){this.cksumValid=!1,this.needPax=!1,this.nullBlock=!1,this.block=null,this.path=null,this.mode=null,this.uid=null,this.gid=null,this.size=null,this.mtime=null,this.cksum=null,this[Q]="0",this.linkpath=null,this.uname=null,this.gname=null,this.devmaj=0,this.devmin=0,this.atime=null,this.ctime=null,Buffer.isBuffer(e)?this.decode(e,t||0,s,i):e&&this.set(e)}decode(e,t,s,i){if(t||(t=0),!e||!(e.length>=t+512))throw new Error("need 512 bytes for header");if(this.path=pt(e,t,100),this.mode=He(e,t+100,8),this.uid=He(e,t+108,8),this.gid=He(e,t+116,8),this.size=He(e,t+124,12),this.mtime=Vi(e,t+136,12),this.cksum=He(e,t+148,12),this[Ji](s),this[Ji](i,!0),this[Q]=pt(e,t+156,1),this[Q]===""&&(this[Q]="0"),this[Q]==="0"&&this.path.slice(-1)==="/"&&(this[Q]="5"),this[Q]==="5"&&(this.size=0),this.linkpath=pt(e,t+157,100),e.slice(t+257,t+265).toString()==="ustar\x0000")if(this.uname=pt(e,t+265,32),this.gname=pt(e,t+297,32),this.devmaj=He(e,t+329,8),this.devmin=He(e,t+337,8),e[t+475]!==0){let o=pt(e,t+345,155);this.path=o+"/"+this.path}else{let o=pt(e,t+345,130);o&&(this.path=o+"/"+this.path),this.atime=Vi(e,t+476,12),this.ctime=Vi(e,t+488,12)}let n=8*32;for(let o=t;o<t+148;o++)n+=e[o];for(let o=t+156;o<t+512;o++)n+=e[o];this.cksumValid=n===this.cksum,this.cksum===null&&n===8*32&&(this.nullBlock=!0)}[Ji](e,t){for(let s in e)e[s]!==null&&e[s]!==void 0&&!(t&&s==="path")&&(this[s]=e[s])}encode(e,t){if(e||(e=this.block=Buffer.alloc(512),t=0),t||(t=0),!(e.length>=t+512))throw new Error("need 512 bytes for header");let s=this.ctime||this.atime?130:155,i=lh(this.path||"",s),n=i[0],o=i[1];this.needPax=i[2],this.needPax=mt(e,t,100,n)||this.needPax,this.needPax=Je(e,t+100,8,this.mode)||this.needPax,this.needPax=Je(e,t+108,8,this.uid)||this.needPax,this.needPax=Je(e,t+116,8,this.gid)||this.needPax,this.needPax=Je(e,t+124,12,this.size)||this.needPax,this.needPax=Yi(e,t+136,12,this.mtime)||this.needPax,e[t+156]=this[Q].charCodeAt(0),this.needPax=mt(e,t+157,100,this.linkpath)||this.needPax,e.write("ustar\x0000",t+257,8),this.needPax=mt(e,t+265,32,this.uname)||this.needPax,this.needPax=mt(e,t+297,32,this.gname)||this.needPax,this.needPax=Je(e,t+329,8,this.devmaj)||this.needPax,this.needPax=Je(e,t+337,8,this.devmin)||this.needPax,this.needPax=mt(e,t+345,s,o)||this.needPax,e[t+475]!==0?this.needPax=mt(e,t+345,155,o)||this.needPax:(this.needPax=mt(e,t+345,130,o)||this.needPax,this.needPax=Yi(e,t+476,12,this.atime)||this.needPax,this.needPax=Yi(e,t+488,12,this.ctime)||this.needPax);let a=8*32;for(let l=t;l<t+148;l++)a+=e[l];for(let l=t+156;l<t+512;l++)a+=e[l];return this.cksum=a,Je(e,t+148,8,this.cksum),this.cksumValid=!0,this.needPax}set(e){for(let t in e)e[t]!==null&&e[t]!==void 0&&(this[t]=e[t])}get type(){return Hi.name.get(this[Q])||this[Q]}get typeKey(){return this[Q]}set type(e){Hi.code.has(e)?this[Q]=Hi.code.get(e):this[Q]=e}},lh=(r,e)=>{let s=r,i="",n,o=Lt.parse(r).root||".";if(Buffer.byteLength(s)<100)n=[s,i,!1];else{i=Lt.dirname(s),s=Lt.basename(s);do Buffer.byteLength(s)<=100&&Buffer.byteLength(i)<=e?n=[s,i,!1]:Buffer.byteLength(s)>100&&Buffer.byteLength(i)<=e?n=[s.slice(0,100-1),i,!0]:(s=Lt.join(Lt.basename(i),s),i=Lt.dirname(i));while(i!==o&&!n);n||(n=[r.slice(0,100-1),"",!0])}return n},pt=(r,e,t)=>r.slice(e,e+t).toString("utf8").replace(/\0.*/,""),Vi=(r,e,t)=>hh(He(r,e,t)),hh=r=>r===null?null:new Date(r*1e3),He=(r,e,t)=>r[e]&128?du.parse(r.slice(e,e+t)):ph(r,e,t),fh=r=>isNaN(r)?null:r,ph=(r,e,t)=>fh(parseInt(r.slice(e,e+t).toString("utf8").replace(/\0.*$/,"").trim(),8)),mh={12:8589934591,8:2097151},Je=(r,e,t,s)=>s===null?!1:s>mh[t]||s<0?(du.encode(s,r.slice(e,e+t)),!0):(dh(r,e,t,s),!1),dh=(r,e,t,s)=>r.write(Dh(s,t),e,t,"ascii"),Dh=(r,e)=>gh(Math.floor(r).toString(8),e),gh=(r,e)=>(r.length===e-1?r:new Array(e-r.length-1).join("0")+r+" ")+"\0",Yi=(r,e,t,s)=>s===null?!1:Je(r,e,t,s.getTime()/1e3),Eh=new Array(156).join("\0"),mt=(r,e,t,s)=>s===null?!1:(r.write(s+Eh,e,t,"utf8"),s.length!==Buffer.byteLength(s)||s.length>t);Du.exports=Ki});var rs=y((Zd,gu)=>{"use strict";u();var yh=Mt(),Ah=A("path"),gr=class{constructor(e,t){this.atime=e.atime||null,this.charset=e.charset||null,this.comment=e.comment||null,this.ctime=e.ctime||null,this.gid=e.gid||null,this.gname=e.gname||null,this.linkpath=e.linkpath||null,this.mtime=e.mtime||null,this.path=e.path||null,this.size=e.size||null,this.uid=e.uid||null,this.uname=e.uname||null,this.dev=e.dev||null,this.ino=e.ino||null,this.nlink=e.nlink||null,this.global=t||!1}encode(){let e=this.encodeBody();if(e==="")return null;let t=Buffer.byteLength(e),s=512*Math.ceil(1+t/512),i=Buffer.allocUnsafe(s);for(let n=0;n<512;n++)i[n]=0;new yh({path:("PaxHeader/"+Ah.basename(this.path)).slice(0,99),mode:this.mode||420,uid:this.uid||null,gid:this.gid||null,size:t,mtime:this.mtime||null,type:this.global?"GlobalExtendedHeader":"ExtendedHeader",linkpath:"",uname:this.uname||"",gname:this.gname||"",devmaj:0,devmin:0,atime:this.atime||null,ctime:this.ctime||null}).encode(i),i.write(e,512,t,"utf8");for(let n=t+512;n<i.length;n++)i[n]=0;return i}encodeBody(){return this.encodeField("path")+this.encodeField("ctime")+this.encodeField("atime")+this.encodeField("dev")+this.encodeField("ino")+this.encodeField("nlink")+this.encodeField("charset")+this.encodeField("comment")+this.encodeField("gid")+this.encodeField("gname")+this.encodeField("linkpath")+this.encodeField("mtime")+this.encodeField("size")+this.encodeField("uid")+this.encodeField("uname")}encodeField(e){if(this[e]===null||this[e]===void 0)return"";let t=this[e]instanceof Date?this[e].getTime()/1e3:this[e],s=" "+(e==="dev"||e==="ino"||e==="nlink"?"SCHILY.":"")+e+"="+t+`
`,i=Buffer.byteLength(s),n=Math.floor(Math.log(i)/Math.log(10))+1;return i+n>=Math.pow(10,n)&&(n+=1),n+i+s}};gr.parse=(r,e,t)=>new gr(Ch(wh(r),e),t);var Ch=(r,e)=>e?Object.keys(r).reduce((t,s)=>(t[s]=r[s],t),e):r,wh=r=>r.replace(/\n$/,"").split(`
`).reduce(Fh,Object.create(null)),Fh=(r,e)=>{let t=parseInt(e,10);if(t!==Buffer.byteLength(e)+1)return r;e=e.slice((t+" ").length);let s=e.split("="),i=s.shift().replace(/^SCHILY\.(dev|ino|nlink)/,"$1");if(!i)return r;let n=s.join("=");return r[i]=/^([A-Z]+\.)?([mac]|birth|creation)time$/.test(i)?new Date(n*1e3):/^[0-9]+$/.test(n)?+n:n,r};gu.exports=gr});var It=y((Xd,Eu)=>{u();Eu.exports=r=>{let e=r.length-1,t=-1;for(;e>-1&&r.charAt(e)==="/";)t=e,e--;return t===-1?r:r.slice(0,t)}});var ss=y((Qd,yu)=>{"use strict";u();yu.exports=r=>class extends r{warn(e,t,s={}){this.file&&(s.file=this.file),this.cwd&&(s.cwd=this.cwd),s.code=t instanceof Error&&t.code||e,s.tarCode=e,!this.strict&&s.recoverable!==!1?(t instanceof Error&&(s=Object.assign(t,s),t=t.message),this.emit("warn",s.tarCode,t,s)):t instanceof Error?this.emit("error",Object.assign(t,s)):this.emit("error",Object.assign(new Error(`${e}: ${t}`),s))}}});var Xi=y((t0,Au)=>{"use strict";u();var is=["|","<",">","?",":"],Zi=is.map(r=>String.fromCharCode(61440+r.charCodeAt(0))),bh=new Map(is.map((r,e)=>[r,Zi[e]])),Sh=new Map(Zi.map((r,e)=>[r,is[e]]));Au.exports={encode:r=>is.reduce((e,t)=>e.split(t).join(bh.get(t)),r),decode:r=>Zi.reduce((e,t)=>e.split(t).join(Sh.get(t)),r)}});var Qi=y((r0,wu)=>{u();var{isAbsolute:kh,parse:Cu}=A("path").win32;wu.exports=r=>{let e="",t=Cu(r);for(;kh(r)||t.root;){let s=r.charAt(0)==="/"&&r.slice(0,4)!=="//?/"?"/":t.root;r=r.slice(s.length),e+=s,t=Cu(r)}return[e,r]}});var bu=y((s0,Fu)=>{"use strict";u();Fu.exports=(r,e,t)=>(r&=4095,t&&(r=(r|384)&-19),e&&(r&256&&(r|=64),r&32&&(r|=8),r&4&&(r|=1)),r)});var ln=y((o0,Iu)=>{"use strict";u();var xu=Gr(),Ou=rs(),Tu=Mt(),ge=A("fs"),Su=A("path"),De=Nt(),_h=It(),Pu=(r,e)=>e?(r=De(r).replace(/^\.(\/|$)/,""),_h(e)+"/"+r):De(r),Rh=16*1024*1024,ku=Symbol("process"),_u=Symbol("file"),Ru=Symbol("directory"),tn=Symbol("symlink"),Bu=Symbol("hardlink"),Er=Symbol("header"),ns=Symbol("read"),rn=Symbol("lstat"),os=Symbol("onlstat"),sn=Symbol("onread"),nn=Symbol("onreadlink"),on=Symbol("openfile"),un=Symbol("onopenfile"),Ve=Symbol("close"),us=Symbol("mode"),an=Symbol("awaitDrain"),en=Symbol("ondrain"),Ee=Symbol("prefix"),vu=Symbol("hadError"),Nu=ss(),Bh=Xi(),Lu=Qi(),Mu=bu(),as=Nu(class extends xu{constructor(e,t){if(t=t||{},super(t),typeof e!="string")throw new TypeError("path is required");this.path=De(e),this.portable=!!t.portable,this.myuid=process.getuid&&process.getuid()||0,this.myuser=process.env.USER||"",this.maxReadSize=t.maxReadSize||Rh,this.linkCache=t.linkCache||new Map,this.statCache=t.statCache||new Map,this.preservePaths=!!t.preservePaths,this.cwd=De(t.cwd||process.cwd()),this.strict=!!t.strict,this.noPax=!!t.noPax,this.noMtime=!!t.noMtime,this.mtime=t.mtime||null,this.prefix=t.prefix?De(t.prefix):null,this.fd=null,this.blockLen=null,this.blockRemain=null,this.buf=null,this.offset=null,this.length=null,this.pos=null,this.remain=null,typeof t.onwarn=="function"&&this.on("warn",t.onwarn);let s=!1;if(!this.preservePaths){let[i,n]=Lu(this.path);i&&(this.path=n,s=i)}this.win32=!!t.win32||process.platform==="win32",this.win32&&(this.path=Bh.decode(this.path.replace(/\\/g,"/")),e=e.replace(/\\/g,"/")),this.absolute=De(t.absolute||Su.resolve(this.cwd,e)),this.path===""&&(this.path="./"),s&&this.warn("TAR_ENTRY_INFO",`stripping ${s} from absolute path`,{entry:this,path:s+this.path}),this.statCache.has(this.absolute)?this[os](this.statCache.get(this.absolute)):this[rn]()}emit(e,...t){return e==="error"&&(this[vu]=!0),super.emit(e,...t)}[rn](){ge.lstat(this.absolute,(e,t)=>{if(e)return this.emit("error",e);this[os](t)})}[os](e){this.statCache.set(this.absolute,e),this.stat=e,e.isFile()||(e.size=0),this.type=xh(e),this.emit("stat",e),this[ku]()}[ku](){switch(this.type){case"File":return this[_u]();case"Directory":return this[Ru]();case"SymbolicLink":return this[tn]();default:return this.end()}}[us](e){return Mu(e,this.type==="Directory",this.portable)}[Ee](e){return Pu(e,this.prefix)}[Er](){this.type==="Directory"&&this.portable&&(this.noMtime=!0),this.header=new Tu({path:this[Ee](this.path),linkpath:this.type==="Link"?this[Ee](this.linkpath):this.linkpath,mode:this[us](this.stat.mode),uid:this.portable?null:this.stat.uid,gid:this.portable?null:this.stat.gid,size:this.stat.size,mtime:this.noMtime?null:this.mtime||this.stat.mtime,type:this.type,uname:this.portable?null:this.stat.uid===this.myuid?this.myuser:"",atime:this.portable?null:this.stat.atime,ctime:this.portable?null:this.stat.ctime}),this.header.encode()&&!this.noPax&&super.write(new Ou({atime:this.portable?null:this.header.atime,ctime:this.portable?null:this.header.ctime,gid:this.portable?null:this.header.gid,mtime:this.noMtime?null:this.mtime||this.header.mtime,path:this[Ee](this.path),linkpath:this.type==="Link"?this[Ee](this.linkpath):this.linkpath,size:this.header.size,uid:this.portable?null:this.header.uid,uname:this.portable?null:this.header.uname,dev:this.portable?null:this.stat.dev,ino:this.portable?null:this.stat.ino,nlink:this.portable?null:this.stat.nlink}).encode()),super.write(this.header.block)}[Ru](){this.path.slice(-1)!=="/"&&(this.path+="/"),this.stat.size=0,this[Er](),this.end()}[tn](){ge.readlink(this.absolute,(e,t)=>{if(e)return this.emit("error",e);this[nn](t)})}[nn](e){this.linkpath=De(e),this[Er](),this.end()}[Bu](e){this.type="Link",this.linkpath=De(Su.relative(this.cwd,e)),this.stat.size=0,this[Er](),this.end()}[_u](){if(this.stat.nlink>1){let e=this.stat.dev+":"+this.stat.ino;if(this.linkCache.has(e)){let t=this.linkCache.get(e);if(t.indexOf(this.cwd)===0)return this[Bu](t)}this.linkCache.set(e,this.absolute)}if(this[Er](),this.stat.size===0)return this.end();this[on]()}[on](){ge.open(this.absolute,"r",(e,t)=>{if(e)return this.emit("error",e);this[un](t)})}[un](e){if(this.fd=e,this[vu])return this[Ve]();this.blockLen=512*Math.ceil(this.stat.size/512),this.blockRemain=this.blockLen;let t=Math.min(this.blockLen,this.maxReadSize);this.buf=Buffer.allocUnsafe(t),this.offset=0,this.pos=0,this.remain=this.stat.size,this.length=this.buf.length,this[ns]()}[ns](){let{fd:e,buf:t,offset:s,length:i,pos:n}=this;ge.read(e,t,s,i,n,(o,a)=>{if(o)return this[Ve](()=>this.emit("error",o));this[sn](a)})}[Ve](e){ge.close(this.fd,e)}[sn](e){if(e<=0&&this.remain>0){let i=new Error("encountered unexpected EOF");return i.path=this.absolute,i.syscall="read",i.code="EOF",this[Ve](()=>this.emit("error",i))}if(e>this.remain){let i=new Error("did not encounter expected EOF");return i.path=this.absolute,i.syscall="read",i.code="EOF",this[Ve](()=>this.emit("error",i))}if(e===this.remain)for(let i=e;i<this.length&&e<this.blockRemain;i++)this.buf[i+this.offset]=0,e++,this.remain++;let t=this.offset===0&&e===this.buf.length?this.buf:this.buf.slice(this.offset,this.offset+e);this.write(t)?this[en]():this[an](()=>this[en]())}[an](e){this.once("drain",e)}write(e){if(this.blockRemain<e.length){let t=new Error("writing more data than expected");return t.path=this.absolute,this.emit("error",t)}return this.remain-=e.length,this.blockRemain-=e.length,this.pos+=e.length,this.offset+=e.length,super.write(e)}[en](){if(!this.remain)return this.blockRemain&&super.write(Buffer.alloc(this.blockRemain)),this[Ve](e=>e?this.emit("error",e):this.end());this.offset>=this.length&&(this.buf=Buffer.allocUnsafe(Math.min(this.blockRemain,this.buf.length)),this.offset=0),this.length=this.buf.length-this.offset,this[ns]()}}),cn=class extends as{[rn](){this[os](ge.lstatSync(this.absolute))}[tn](){this[nn](ge.readlinkSync(this.absolute))}[on](){this[un](ge.openSync(this.absolute,"r"))}[ns](){let e=!0;try{let{fd:t,buf:s,offset:i,length:n,pos:o}=this,a=ge.readSync(t,s,i,n,o);this[sn](a),e=!1}finally{if(e)try{this[Ve](()=>{})}catch{}}}[an](e){e()}[Ve](e){ge.closeSync(this.fd),e()}},vh=Nu(class extends xu{constructor(e,t){t=t||{},super(t),this.preservePaths=!!t.preservePaths,this.portable=!!t.portable,this.strict=!!t.strict,this.noPax=!!t.noPax,this.noMtime=!!t.noMtime,this.readEntry=e,this.type=e.type,this.type==="Directory"&&this.portable&&(this.noMtime=!0),this.prefix=t.prefix||null,this.path=De(e.path),this.mode=this[us](e.mode),this.uid=this.portable?null:e.uid,this.gid=this.portable?null:e.gid,this.uname=this.portable?null:e.uname,this.gname=this.portable?null:e.gname,this.size=e.size,this.mtime=this.noMtime?null:t.mtime||e.mtime,this.atime=this.portable?null:e.atime,this.ctime=this.portable?null:e.ctime,this.linkpath=De(e.linkpath),typeof t.onwarn=="function"&&this.on("warn",t.onwarn);let s=!1;if(!this.preservePaths){let[i,n]=Lu(this.path);i&&(this.path=n,s=i)}this.remain=e.size,this.blockRemain=e.startBlockSize,this.header=new Tu({path:this[Ee](this.path),linkpath:this.type==="Link"?this[Ee](this.linkpath):this.linkpath,mode:this.mode,uid:this.portable?null:this.uid,gid:this.portable?null:this.gid,size:this.size,mtime:this.noMtime?null:this.mtime,type:this.type,uname:this.portable?null:this.uname,atime:this.portable?null:this.atime,ctime:this.portable?null:this.ctime}),s&&this.warn("TAR_ENTRY_INFO",`stripping ${s} from absolute path`,{entry:this,path:s+this.path}),this.header.encode()&&!this.noPax&&super.write(new Ou({atime:this.portable?null:this.atime,ctime:this.portable?null:this.ctime,gid:this.portable?null:this.gid,mtime:this.noMtime?null:this.mtime,path:this[Ee](this.path),linkpath:this.type==="Link"?this[Ee](this.linkpath):this.linkpath,size:this.size,uid:this.portable?null:this.uid,uname:this.portable?null:this.uname,dev:this.portable?null:this.readEntry.dev,ino:this.portable?null:this.readEntry.ino,nlink:this.portable?null:this.readEntry.nlink}).encode()),super.write(this.header.block),e.pipe(this)}[Ee](e){return Pu(e,this.prefix)}[us](e){return Mu(e,this.type==="Directory",this.portable)}write(e){let t=e.length;if(t>this.blockRemain)throw new Error("writing more to entry than is appropriate");return this.blockRemain-=t,super.write(e)}end(){return this.blockRemain&&super.write(Buffer.alloc(this.blockRemain)),super.end()}});as.Sync=cn;as.Tar=vh;var xh=r=>r.isFile()?"File":r.isDirectory()?"Directory":r.isSymbolicLink()?"SymbolicLink":"Unsupported";Iu.exports=as});var qu=y((u0,ju)=>{"use strict";u();ju.exports=function(r){r.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}});var hn=y((a0,Uu)=>{"use strict";u();Uu.exports=_;_.Node=dt;_.create=_;function _(r){var e=this;if(e instanceof _||(e=new _),e.tail=null,e.head=null,e.length=0,r&&typeof r.forEach=="function")r.forEach(function(i){e.push(i)});else if(arguments.length>0)for(var t=0,s=arguments.length;t<s;t++)e.push(arguments[t]);return e}_.prototype.removeNode=function(r){if(r.list!==this)throw new Error("removing node which does not belong to this list");var e=r.next,t=r.prev;return e&&(e.prev=t),t&&(t.next=e),r===this.head&&(this.head=e),r===this.tail&&(this.tail=t),r.list.length--,r.next=null,r.prev=null,r.list=null,e};_.prototype.unshiftNode=function(r){if(r!==this.head){r.list&&r.list.removeNode(r);var e=this.head;r.list=this,r.next=e,e&&(e.prev=r),this.head=r,this.tail||(this.tail=r),this.length++}};_.prototype.pushNode=function(r){if(r!==this.tail){r.list&&r.list.removeNode(r);var e=this.tail;r.list=this,r.prev=e,e&&(e.next=r),this.tail=r,this.head||(this.head=r),this.length++}};_.prototype.push=function(){for(var r=0,e=arguments.length;r<e;r++)Th(this,arguments[r]);return this.length};_.prototype.unshift=function(){for(var r=0,e=arguments.length;r<e;r++)Ph(this,arguments[r]);return this.length};_.prototype.pop=function(){if(!!this.tail){var r=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,r}};_.prototype.shift=function(){if(!!this.head){var r=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,r}};_.prototype.forEach=function(r,e){e=e||this;for(var t=this.head,s=0;t!==null;s++)r.call(e,t.value,s,this),t=t.next};_.prototype.forEachReverse=function(r,e){e=e||this;for(var t=this.tail,s=this.length-1;t!==null;s--)r.call(e,t.value,s,this),t=t.prev};_.prototype.get=function(r){for(var e=0,t=this.head;t!==null&&e<r;e++)t=t.next;if(e===r&&t!==null)return t.value};_.prototype.getReverse=function(r){for(var e=0,t=this.tail;t!==null&&e<r;e++)t=t.prev;if(e===r&&t!==null)return t.value};_.prototype.map=function(r,e){e=e||this;for(var t=new _,s=this.head;s!==null;)t.push(r.call(e,s.value,this)),s=s.next;return t};_.prototype.mapReverse=function(r,e){e=e||this;for(var t=new _,s=this.tail;s!==null;)t.push(r.call(e,s.value,this)),s=s.prev;return t};_.prototype.reduce=function(r,e){var t,s=this.head;if(arguments.length>1)t=e;else if(this.head)s=this.head.next,t=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var i=0;s!==null;i++)t=r(t,s.value,i),s=s.next;return t};_.prototype.reduceReverse=function(r,e){var t,s=this.tail;if(arguments.length>1)t=e;else if(this.tail)s=this.tail.prev,t=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var i=this.length-1;s!==null;i--)t=r(t,s.value,i),s=s.prev;return t};_.prototype.toArray=function(){for(var r=new Array(this.length),e=0,t=this.head;t!==null;e++)r[e]=t.value,t=t.next;return r};_.prototype.toArrayReverse=function(){for(var r=new Array(this.length),e=0,t=this.tail;t!==null;e++)r[e]=t.value,t=t.prev;return r};_.prototype.slice=function(r,e){e=e||this.length,e<0&&(e+=this.length),r=r||0,r<0&&(r+=this.length);var t=new _;if(e<r||e<0)return t;r<0&&(r=0),e>this.length&&(e=this.length);for(var s=0,i=this.head;i!==null&&s<r;s++)i=i.next;for(;i!==null&&s<e;s++,i=i.next)t.push(i.value);return t};_.prototype.sliceReverse=function(r,e){e=e||this.length,e<0&&(e+=this.length),r=r||0,r<0&&(r+=this.length);var t=new _;if(e<r||e<0)return t;r<0&&(r=0),e>this.length&&(e=this.length);for(var s=this.length,i=this.tail;i!==null&&s>e;s--)i=i.prev;for(;i!==null&&s>r;s--,i=i.prev)t.push(i.value);return t};_.prototype.splice=function(r,e,...t){r>this.length&&(r=this.length-1),r<0&&(r=this.length+r);for(var s=0,i=this.head;i!==null&&s<r;s++)i=i.next;for(var n=[],s=0;i&&s<e;s++)n.push(i.value),i=this.removeNode(i);i===null&&(i=this.tail),i!==this.head&&i!==this.tail&&(i=i.prev);for(var s=0;s<t.length;s++)i=Oh(this,i,t[s]);return n};_.prototype.reverse=function(){for(var r=this.head,e=this.tail,t=r;t!==null;t=t.prev){var s=t.prev;t.prev=t.next,t.next=s}return this.head=e,this.tail=r,this};function Oh(r,e,t){var s=e===r.head?new dt(t,null,e,r):new dt(t,e,e.next,r);return s.next===null&&(r.tail=s),s.prev===null&&(r.head=s),r.length++,s}function Th(r,e){r.tail=new dt(e,r.tail,null,r),r.head||(r.head=r.tail),r.length++}function Ph(r,e){r.head=new dt(e,null,r.head,r),r.tail||(r.tail=r.head),r.length++}function dt(r,e,t,s){if(!(this instanceof dt))return new dt(r,e,t,s);this.list=s,this.value=r,e?(e.next=this,this.prev=e):this.prev=null,t?(t.prev=this,this.next=t):this.next=null}try{qu()(_)}catch{}});var gs=y((l0,Vu)=>{"use strict";u();var ds=class{constructor(e,t){this.path=e||"./",this.absolute=t,this.entry=null,this.stat=null,this.readdir=null,this.pending=!1,this.ignore=!1,this.piped=!1}},Nh=Gr(),Lh=$i(),Mh=es(),An=ln(),Ih=An.Sync,jh=An.Tar,qh=hn(),$u=Buffer.alloc(1024),hs=Symbol("onStat"),cs=Symbol("ended"),ye=Symbol("queue"),jt=Symbol("current"),Dt=Symbol("process"),ls=Symbol("processing"),zu=Symbol("processJob"),Ae=Symbol("jobs"),fn=Symbol("jobDone"),fs=Symbol("addFSEntry"),Wu=Symbol("addTarEntry"),Dn=Symbol("stat"),gn=Symbol("readdir"),ps=Symbol("onreaddir"),ms=Symbol("pipe"),Gu=Symbol("entry"),pn=Symbol("entryOpt"),En=Symbol("writeEntryClass"),Ju=Symbol("write"),mn=Symbol("ondrain"),Ds=A("fs"),Hu=A("path"),Uh=ss(),dn=Nt(),Cn=Uh(class extends Nh{constructor(e){super(e),e=e||Object.create(null),this.opt=e,this.file=e.file||"",this.cwd=e.cwd||process.cwd(),this.maxReadSize=e.maxReadSize,this.preservePaths=!!e.preservePaths,this.strict=!!e.strict,this.noPax=!!e.noPax,this.prefix=dn(e.prefix||""),this.linkCache=e.linkCache||new Map,this.statCache=e.statCache||new Map,this.readdirCache=e.readdirCache||new Map,this[En]=An,typeof e.onwarn=="function"&&this.on("warn",e.onwarn),this.portable=!!e.portable,this.zip=null,e.gzip?(typeof e.gzip!="object"&&(e.gzip={}),this.portable&&(e.gzip.portable=!0),this.zip=new Lh.Gzip(e.gzip),this.zip.on("data",t=>super.write(t)),this.zip.on("end",t=>super.end()),this.zip.on("drain",t=>this[mn]()),this.on("resume",t=>this.zip.resume())):this.on("drain",this[mn]),this.noDirRecurse=!!e.noDirRecurse,this.follow=!!e.follow,this.noMtime=!!e.noMtime,this.mtime=e.mtime||null,this.filter=typeof e.filter=="function"?e.filter:t=>!0,this[ye]=new qh,this[Ae]=0,this.jobs=+e.jobs||4,this[ls]=!1,this[cs]=!1}[Ju](e){return super.write(e)}add(e){return this.write(e),this}end(e){return e&&this.write(e),this[cs]=!0,this[Dt](),this}write(e){if(this[cs])throw new Error("write after end");return e instanceof Mh?this[Wu](e):this[fs](e),this.flowing}[Wu](e){let t=dn(Hu.resolve(this.cwd,e.path));if(!this.filter(e.path,e))e.resume();else{let s=new ds(e.path,t,!1);s.entry=new jh(e,this[pn](s)),s.entry.on("end",i=>this[fn](s)),this[Ae]+=1,this[ye].push(s)}this[Dt]()}[fs](e){let t=dn(Hu.resolve(this.cwd,e));this[ye].push(new ds(e,t)),this[Dt]()}[Dn](e){e.pending=!0,this[Ae]+=1;let t=this.follow?"stat":"lstat";Ds[t](e.absolute,(s,i)=>{e.pending=!1,this[Ae]-=1,s?this.emit("error",s):this[hs](e,i)})}[hs](e,t){this.statCache.set(e.absolute,t),e.stat=t,this.filter(e.path,t)||(e.ignore=!0),this[Dt]()}[gn](e){e.pending=!0,this[Ae]+=1,Ds.readdir(e.absolute,(t,s)=>{if(e.pending=!1,this[Ae]-=1,t)return this.emit("error",t);this[ps](e,s)})}[ps](e,t){this.readdirCache.set(e.absolute,t),e.readdir=t,this[Dt]()}[Dt](){if(!this[ls]){this[ls]=!0;for(let e=this[ye].head;e!==null&&this[Ae]<this.jobs;e=e.next)if(this[zu](e.value),e.value.ignore){let t=e.next;this[ye].removeNode(e),e.next=t}this[ls]=!1,this[cs]&&!this[ye].length&&this[Ae]===0&&(this.zip?this.zip.end($u):(super.write($u),super.end()))}}get[jt](){return this[ye]&&this[ye].head&&this[ye].head.value}[fn](e){this[ye].shift(),this[Ae]-=1,this[Dt]()}[zu](e){if(!e.pending){if(e.entry){e===this[jt]&&!e.piped&&this[ms](e);return}if(e.stat||(this.statCache.has(e.absolute)?this[hs](e,this.statCache.get(e.absolute)):this[Dn](e)),!!e.stat&&!e.ignore&&!(!this.noDirRecurse&&e.stat.isDirectory()&&!e.readdir&&(this.readdirCache.has(e.absolute)?this[ps](e,this.readdirCache.get(e.absolute)):this[gn](e),!e.readdir))){if(e.entry=this[Gu](e),!e.entry){e.ignore=!0;return}e===this[jt]&&!e.piped&&this[ms](e)}}}[pn](e){return{onwarn:(t,s,i)=>this.warn(t,s,i),noPax:this.noPax,cwd:this.cwd,absolute:e.absolute,preservePaths:this.preservePaths,maxReadSize:this.maxReadSize,strict:this.strict,portable:this.portable,linkCache:this.linkCache,statCache:this.statCache,noMtime:this.noMtime,mtime:this.mtime,prefix:this.prefix}}[Gu](e){this[Ae]+=1;try{return new this[En](e.path,this[pn](e)).on("end",()=>this[fn](e)).on("error",t=>this.emit("error",t))}catch(t){this.emit("error",t)}}[mn](){this[jt]&&this[jt].entry&&this[jt].entry.resume()}[ms](e){e.piped=!0,e.readdir&&e.readdir.forEach(i=>{let n=e.path,o=n==="./"?"":n.replace(/\/*$/,"/");this[fs](o+i)});let t=e.entry,s=this.zip;s?t.on("data",i=>{s.write(i)||t.pause()}):t.on("data",i=>{super.write(i)||t.pause()})}pause(){return this.zip&&this.zip.pause(),super.pause()}}),yn=class extends Cn{constructor(e){super(e),this[En]=Ih}pause(){}resume(){}[Dn](e){let t=this.follow?"statSync":"lstatSync";this[hs](e,Ds[t](e.absolute))}[gn](e,t){this[ps](e,Ds.readdirSync(e.absolute))}[ms](e){let t=e.entry,s=this.zip;e.readdir&&e.readdir.forEach(i=>{let n=e.path,o=n==="./"?"":n.replace(/\/*$/,"/");this[fs](o+i)}),s?t.on("data",i=>{s.write(i)}):t.on("data",i=>{super[Ju](i)})}};Cn.Sync=yn;Vu.exports=Cn});var Jt=y(Ar=>{"use strict";u();var $h=ki(),zh=A("events").EventEmitter,K=A("fs"),bn=K.writev;if(!bn){let r=process.binding("fs"),e=r.FSReqWrap||r.FSReqCallback;bn=(t,s,i,n)=>{let o=(l,c)=>n(l,c,s),a=new e;a.oncomplete=o,r.writeBuffers(t,s,i,a)}}var Gt=Symbol("_autoClose"),fe=Symbol("_close"),yr=Symbol("_ended"),v=Symbol("_fd"),Yu=Symbol("_finished"),Ke=Symbol("_flags"),wn=Symbol("_flush"),Sn=Symbol("_handleChunk"),kn=Symbol("_makeBuf"),ws=Symbol("_mode"),Es=Symbol("_needDrain"),zt=Symbol("_onerror"),Ht=Symbol("_onopen"),Fn=Symbol("_onread"),Ut=Symbol("_onwrite"),Ze=Symbol("_open"),Ne=Symbol("_path"),gt=Symbol("_pos"),Ce=Symbol("_queue"),$t=Symbol("_read"),Ku=Symbol("_readSize"),Ye=Symbol("_reading"),ys=Symbol("_remain"),Zu=Symbol("_size"),As=Symbol("_write"),qt=Symbol("_writing"),Cs=Symbol("_defaultFlag"),Wt=Symbol("_errored"),Fs=class extends $h{constructor(e,t){if(t=t||{},super(t),this.readable=!0,this.writable=!1,typeof e!="string")throw new TypeError("path must be a string");this[Wt]=!1,this[v]=typeof t.fd=="number"?t.fd:null,this[Ne]=e,this[Ku]=t.readSize||16*1024*1024,this[Ye]=!1,this[Zu]=typeof t.size=="number"?t.size:1/0,this[ys]=this[Zu],this[Gt]=typeof t.autoClose=="boolean"?t.autoClose:!0,typeof this[v]=="number"?this[$t]():this[Ze]()}get fd(){return this[v]}get path(){return this[Ne]}write(){throw new TypeError("this is a readable stream")}end(){throw new TypeError("this is a readable stream")}[Ze](){K.open(this[Ne],"r",(e,t)=>this[Ht](e,t))}[Ht](e,t){e?this[zt](e):(this[v]=t,this.emit("open",t),this[$t]())}[kn](){return Buffer.allocUnsafe(Math.min(this[Ku],this[ys]))}[$t](){if(!this[Ye]){this[Ye]=!0;let e=this[kn]();if(e.length===0)return process.nextTick(()=>this[Fn](null,0,e));K.read(this[v],e,0,e.length,null,(t,s,i)=>this[Fn](t,s,i))}}[Fn](e,t,s){this[Ye]=!1,e?this[zt](e):this[Sn](t,s)&&this[$t]()}[fe](){if(this[Gt]&&typeof this[v]=="number"){let e=this[v];this[v]=null,K.close(e,t=>t?this.emit("error",t):this.emit("close"))}}[zt](e){this[Ye]=!0,this[fe](),this.emit("error",e)}[Sn](e,t){let s=!1;return this[ys]-=e,e>0&&(s=super.write(e<t.length?t.slice(0,e):t)),(e===0||this[ys]<=0)&&(s=!1,this[fe](),super.end()),s}emit(e,t){switch(e){case"prefinish":case"finish":break;case"drain":typeof this[v]=="number"&&this[$t]();break;case"error":return this[Wt]?void 0:(this[Wt]=!0,super.emit(e,t));default:return super.emit(e,t)}}},_n=class extends Fs{[Ze](){let e=!0;try{this[Ht](null,K.openSync(this[Ne],"r")),e=!1}finally{e&&this[fe]()}}[$t](){let e=!0;try{if(!this[Ye]){this[Ye]=!0;do{let t=this[kn](),s=t.length===0?0:K.readSync(this[v],t,0,t.length,null);if(!this[Sn](s,t))break}while(!0);this[Ye]=!1}e=!1}finally{e&&this[fe]()}}[fe](){if(this[Gt]&&typeof this[v]=="number"){let e=this[v];this[v]=null,K.closeSync(e),this.emit("close")}}},bs=class extends zh{constructor(e,t){t=t||{},super(t),this.readable=!1,this.writable=!0,this[Wt]=!1,this[qt]=!1,this[yr]=!1,this[Es]=!1,this[Ce]=[],this[Ne]=e,this[v]=typeof t.fd=="number"?t.fd:null,this[ws]=t.mode===void 0?438:t.mode,this[gt]=typeof t.start=="number"?t.start:null,this[Gt]=typeof t.autoClose=="boolean"?t.autoClose:!0;let s=this[gt]!==null?"r+":"w";this[Cs]=t.flags===void 0,this[Ke]=this[Cs]?s:t.flags,this[v]===null&&this[Ze]()}emit(e,t){if(e==="error"){if(this[Wt])return;this[Wt]=!0}return super.emit(e,t)}get fd(){return this[v]}get path(){return this[Ne]}[zt](e){this[fe](),this[qt]=!0,this.emit("error",e)}[Ze](){K.open(this[Ne],this[Ke],this[ws],(e,t)=>this[Ht](e,t))}[Ht](e,t){this[Cs]&&this[Ke]==="r+"&&e&&e.code==="ENOENT"?(this[Ke]="w",this[Ze]()):e?this[zt](e):(this[v]=t,this.emit("open",t),this[wn]())}end(e,t){return e&&this.write(e,t),this[yr]=!0,!this[qt]&&!this[Ce].length&&typeof this[v]=="number"&&this[Ut](null,0),this}write(e,t){return typeof e=="string"&&(e=Buffer.from(e,t)),this[yr]?(this.emit("error",new Error("write() after end()")),!1):this[v]===null||this[qt]||this[Ce].length?(this[Ce].push(e),this[Es]=!0,!1):(this[qt]=!0,this[As](e),!0)}[As](e){K.write(this[v],e,0,e.length,this[gt],(t,s)=>this[Ut](t,s))}[Ut](e,t){e?this[zt](e):(this[gt]!==null&&(this[gt]+=t),this[Ce].length?this[wn]():(this[qt]=!1,this[yr]&&!this[Yu]?(this[Yu]=!0,this[fe](),this.emit("finish")):this[Es]&&(this[Es]=!1,this.emit("drain"))))}[wn](){if(this[Ce].length===0)this[yr]&&this[Ut](null,0);else if(this[Ce].length===1)this[As](this[Ce].pop());else{let e=this[Ce];this[Ce]=[],bn(this[v],e,this[gt],(t,s)=>this[Ut](t,s))}}[fe](){if(this[Gt]&&typeof this[v]=="number"){let e=this[v];this[v]=null,K.close(e,t=>t?this.emit("error",t):this.emit("close"))}}},Rn=class extends bs{[Ze](){let e;if(this[Cs]&&this[Ke]==="r+")try{e=K.openSync(this[Ne],this[Ke],this[ws])}catch(t){if(t.code==="ENOENT")return this[Ke]="w",this[Ze]();throw t}else e=K.openSync(this[Ne],this[Ke],this[ws]);this[Ht](null,e)}[fe](){if(this[Gt]&&typeof this[v]=="number"){let e=this[v];this[v]=null,K.closeSync(e),this.emit("close")}}[As](e){let t=!0;try{this[Ut](null,K.writeSync(this[v],e,0,e.length,this[gt])),t=!1}finally{if(t)try{this[fe]()}catch{}}}};Ar.ReadStream=Fs;Ar.ReadStreamSync=_n;Ar.WriteStream=bs;Ar.WriteStreamSync=Rn});var xs=y((p0,na)=>{"use strict";u();var Wh=ss(),Gh=Mt(),Hh=A("events"),Jh=hn(),Vh=1024*1024,Yh=es(),Xu=rs(),Kh=$i(),{nextTick:Zh}=A("process"),Bn=Buffer.from([31,139]),se=Symbol("state"),Et=Symbol("writeEntry"),Le=Symbol("readEntry"),vn=Symbol("nextEntry"),Qu=Symbol("processEntry"),ie=Symbol("extendedHeader"),Cr=Symbol("globalExtendedHeader"),Xe=Symbol("meta"),ea=Symbol("emitMeta"),T=Symbol("buffer"),Me=Symbol("queue"),yt=Symbol("ended"),ta=Symbol("emittedEnd"),At=Symbol("emit"),Z=Symbol("unzip"),Ss=Symbol("consumeChunk"),ks=Symbol("consumeChunkSub"),xn=Symbol("consumeBody"),ra=Symbol("consumeMeta"),sa=Symbol("consumeHeader"),_s=Symbol("consuming"),On=Symbol("bufferConcat"),Tn=Symbol("maybeEnd"),wr=Symbol("writing"),Qe=Symbol("aborted"),Rs=Symbol("onDone"),Ct=Symbol("sawValidEntry"),Bs=Symbol("sawNullBlock"),vs=Symbol("sawEOF"),ia=Symbol("closeStream"),Xh=r=>!0;na.exports=Wh(class extends Hh{constructor(e){e=e||{},super(e),this.file=e.file||"",this[Ct]=null,this.on(Rs,t=>{(this[se]==="begin"||this[Ct]===!1)&&this.warn("TAR_BAD_ARCHIVE","Unrecognized archive format")}),e.ondone?this.on(Rs,e.ondone):this.on(Rs,t=>{this.emit("prefinish"),this.emit("finish"),this.emit("end")}),this.strict=!!e.strict,this.maxMetaEntrySize=e.maxMetaEntrySize||Vh,this.filter=typeof e.filter=="function"?e.filter:Xh,this.writable=!0,this.readable=!1,this[Me]=new Jh,this[T]=null,this[Le]=null,this[Et]=null,this[se]="begin",this[Xe]="",this[ie]=null,this[Cr]=null,this[yt]=!1,this[Z]=null,this[Qe]=!1,this[Bs]=!1,this[vs]=!1,this.on("end",()=>this[ia]()),typeof e.onwarn=="function"&&this.on("warn",e.onwarn),typeof e.onentry=="function"&&this.on("entry",e.onentry)}[sa](e,t){this[Ct]===null&&(this[Ct]=!1);let s;try{s=new Gh(e,t,this[ie],this[Cr])}catch(i){return this.warn("TAR_ENTRY_INVALID",i)}if(s.nullBlock)this[Bs]?(this[vs]=!0,this[se]==="begin"&&(this[se]="header"),this[At]("eof")):(this[Bs]=!0,this[At]("nullBlock"));else if(this[Bs]=!1,!s.cksumValid)this.warn("TAR_ENTRY_INVALID","checksum failure",{header:s});else if(!s.path)this.warn("TAR_ENTRY_INVALID","path is required",{header:s});else{let i=s.type;if(/^(Symbolic)?Link$/.test(i)&&!s.linkpath)this.warn("TAR_ENTRY_INVALID","linkpath required",{header:s});else if(!/^(Symbolic)?Link$/.test(i)&&s.linkpath)this.warn("TAR_ENTRY_INVALID","linkpath forbidden",{header:s});else{let n=this[Et]=new Yh(s,this[ie],this[Cr]);if(!this[Ct])if(n.remain){let o=()=>{n.invalid||(this[Ct]=!0)};n.on("end",o)}else this[Ct]=!0;n.meta?n.size>this.maxMetaEntrySize?(n.ignore=!0,this[At]("ignoredEntry",n),this[se]="ignore",n.resume()):n.size>0&&(this[Xe]="",n.on("data",o=>this[Xe]+=o),this[se]="meta"):(this[ie]=null,n.ignore=n.ignore||!this.filter(n.path,n),n.ignore?(this[At]("ignoredEntry",n),this[se]=n.remain?"ignore":"header",n.resume()):(n.remain?this[se]="body":(this[se]="header",n.end()),this[Le]?this[Me].push(n):(this[Me].push(n),this[vn]())))}}}[ia](){Zh(()=>this.emit("close"))}[Qu](e){let t=!0;return e?Array.isArray(e)?this.emit.apply(this,e):(this[Le]=e,this.emit("entry",e),e.emittedEnd||(e.on("end",s=>this[vn]()),t=!1)):(this[Le]=null,t=!1),t}[vn](){do;while(this[Qu](this[Me].shift()));if(!this[Me].length){let e=this[Le];!e||e.flowing||e.size===e.remain?this[wr]||this.emit("drain"):e.once("drain",s=>this.emit("drain"))}}[xn](e,t){let s=this[Et],i=s.blockRemain,n=i>=e.length&&t===0?e:e.slice(t,t+i);return s.write(n),s.blockRemain||(this[se]="header",this[Et]=null,s.end()),n.length}[ra](e,t){let s=this[Et],i=this[xn](e,t);return this[Et]||this[ea](s),i}[At](e,t,s){!this[Me].length&&!this[Le]?this.emit(e,t,s):this[Me].push([e,t,s])}[ea](e){switch(this[At]("meta",this[Xe]),e.type){case"ExtendedHeader":case"OldExtendedHeader":this[ie]=Xu.parse(this[Xe],this[ie],!1);break;case"GlobalExtendedHeader":this[Cr]=Xu.parse(this[Xe],this[Cr],!0);break;case"NextFileHasLongPath":case"OldGnuLongPath":this[ie]=this[ie]||Object.create(null),this[ie].path=this[Xe].replace(/\0.*/,"");break;case"NextFileHasLongLinkpath":this[ie]=this[ie]||Object.create(null),this[ie].linkpath=this[Xe].replace(/\0.*/,"");break;default:throw new Error("unknown meta: "+e.type)}}abort(e){this[Qe]=!0,this.emit("abort",e),this.warn("TAR_ABORT",e,{recoverable:!1})}write(e){if(this[Qe])return;if(this[Z]===null&&e){if(this[T]&&(e=Buffer.concat([this[T],e]),this[T]=null),e.length<Bn.length)return this[T]=e,!0;for(let s=0;this[Z]===null&&s<Bn.length;s++)e[s]!==Bn[s]&&(this[Z]=!1);if(this[Z]===null){let s=this[yt];this[yt]=!1,this[Z]=new Kh.Unzip,this[Z].on("data",n=>this[Ss](n)),this[Z].on("error",n=>this.abort(n)),this[Z].on("end",n=>{this[yt]=!0,this[Ss]()}),this[wr]=!0;let i=this[Z][s?"end":"write"](e);return this[wr]=!1,i}}this[wr]=!0,this[Z]?this[Z].write(e):this[Ss](e),this[wr]=!1;let t=this[Me].length?!1:this[Le]?this[Le].flowing:!0;return!t&&!this[Me].length&&this[Le].once("drain",s=>this.emit("drain")),t}[On](e){e&&!this[Qe]&&(this[T]=this[T]?Buffer.concat([this[T],e]):e)}[Tn](){if(this[yt]&&!this[ta]&&!this[Qe]&&!this[_s]){this[ta]=!0;let e=this[Et];if(e&&e.blockRemain){let t=this[T]?this[T].length:0;this.warn("TAR_BAD_ARCHIVE",`Truncated input (needed ${e.blockRemain} more bytes, only ${t} available)`,{entry:e}),this[T]&&e.write(this[T]),e.end()}this[At](Rs)}}[Ss](e){if(this[_s])this[On](e);else if(!e&&!this[T])this[Tn]();else{if(this[_s]=!0,this[T]){this[On](e);let t=this[T];this[T]=null,this[ks](t)}else this[ks](e);for(;this[T]&&this[T].length>=512&&!this[Qe]&&!this[vs];){let t=this[T];this[T]=null,this[ks](t)}this[_s]=!1}(!this[T]||this[yt])&&this[Tn]()}[ks](e){let t=0,s=e.length;for(;t+512<=s&&!this[Qe]&&!this[vs];)switch(this[se]){case"begin":case"header":this[sa](e,t),t+=512;break;case"ignore":case"body":t+=this[xn](e,t);break;case"meta":t+=this[ra](e,t);break;default:throw new Error("invalid state: "+this[se])}t<s&&(this[T]?this[T]=Buffer.concat([e.slice(t),this[T]]):this[T]=e.slice(t))}end(e){this[Qe]||(this[Z]?this[Z].end(e):(this[yt]=!0,this.write(e)))}})});var Os=y((m0,ca)=>{"use strict";u();var Qh=vt(),ua=xs(),Vt=A("fs"),ef=Jt(),oa=A("path"),Pn=It();ca.exports=(r,e,t)=>{typeof r=="function"?(t=r,e=null,r={}):Array.isArray(r)&&(e=r,r={}),typeof e=="function"&&(t=e,e=null),e?e=Array.from(e):e=[];let s=Qh(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return e.length&&rf(s,e),s.noResume||tf(s),s.file&&s.sync?sf(s):s.file?nf(s,t):aa(s)};var tf=r=>{let e=r.onentry;r.onentry=e?t=>{e(t),t.resume()}:t=>t.resume()},rf=(r,e)=>{let t=new Map(e.map(n=>[Pn(n),!0])),s=r.filter,i=(n,o)=>{let a=o||oa.parse(n).root||".",l=n===a?!1:t.has(n)?t.get(n):i(oa.dirname(n),a);return t.set(n,l),l};r.filter=s?(n,o)=>s(n,o)&&i(Pn(n)):n=>i(Pn(n))},sf=r=>{let e=aa(r),t=r.file,s=!0,i;try{let n=Vt.statSync(t),o=r.maxReadSize||16*1024*1024;if(n.size<o)e.end(Vt.readFileSync(t));else{let a=0,l=Buffer.allocUnsafe(o);for(i=Vt.openSync(t,"r");a<n.size;){let c=Vt.readSync(i,l,0,o,a);a+=c,e.write(l.slice(0,c))}e.end()}s=!1}finally{if(s&&i)try{Vt.closeSync(i)}catch{}}},nf=(r,e)=>{let t=new ua(r),s=r.maxReadSize||16*1024*1024,i=r.file,n=new Promise((o,a)=>{t.on("error",a),t.on("end",o),Vt.stat(i,(l,c)=>{if(l)a(l);else{let h=new ef.ReadStream(i,{readSize:s,size:c.size});h.on("error",a),h.pipe(t)}})});return e?n.then(e,e):n},aa=r=>new ua(r)});var da=y((d0,ma)=>{"use strict";u();var of=vt(),Ts=gs(),la=Jt(),ha=Os(),fa=A("path");ma.exports=(r,e,t)=>{if(typeof e=="function"&&(t=e),Array.isArray(r)&&(e=r,r={}),!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");e=Array.from(e);let s=of(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return s.file&&s.sync?uf(s,e):s.file?af(s,e,t):s.sync?cf(s,e):lf(s,e)};var uf=(r,e)=>{let t=new Ts.Sync(r),s=new la.WriteStreamSync(r.file,{mode:r.mode||438});t.pipe(s),pa(t,e)},af=(r,e,t)=>{let s=new Ts(r),i=new la.WriteStream(r.file,{mode:r.mode||438});s.pipe(i);let n=new Promise((o,a)=>{i.on("error",a),i.on("close",o),s.on("error",a)});return Nn(s,e),t?n.then(t,t):n},pa=(r,e)=>{e.forEach(t=>{t.charAt(0)==="@"?ha({file:fa.resolve(r.cwd,t.slice(1)),sync:!0,noResume:!0,onentry:s=>r.add(s)}):r.add(t)}),r.end()},Nn=(r,e)=>{for(;e.length;){let t=e.shift();if(t.charAt(0)==="@")return ha({file:fa.resolve(r.cwd,t.slice(1)),noResume:!0,onentry:s=>r.add(s)}).then(s=>Nn(r,e));r.add(t)}r.end()},cf=(r,e)=>{let t=new Ts.Sync(r);return pa(t,e),t},lf=(r,e)=>{let t=new Ts(r);return Nn(t,e),t}});var Ln=y((D0,wa)=>{"use strict";u();var hf=vt(),Da=gs(),ee=A("fs"),ga=Jt(),Ea=Os(),ya=A("path"),Aa=Mt();wa.exports=(r,e,t)=>{let s=hf(r);if(!s.file)throw new TypeError("file is required");if(s.gzip)throw new TypeError("cannot append to compressed archives");if(!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");return e=Array.from(e),s.sync?ff(s,e):mf(s,e,t)};var ff=(r,e)=>{let t=new Da.Sync(r),s=!0,i,n;try{try{i=ee.openSync(r.file,"r+")}catch(l){if(l.code==="ENOENT")i=ee.openSync(r.file,"w+");else throw l}let o=ee.fstatSync(i),a=Buffer.alloc(512);e:for(n=0;n<o.size;n+=512){for(let h=0,d=0;h<512;h+=d){if(d=ee.readSync(i,a,h,a.length-h,n+h),n===0&&a[0]===31&&a[1]===139)throw new Error("cannot append to compressed archives");if(!d)break e}let l=new Aa(a);if(!l.cksumValid)break;let c=512*Math.ceil(l.size/512);if(n+c+512>o.size)break;n+=c,r.mtimeCache&&r.mtimeCache.set(l.path,l.mtime)}s=!1,pf(r,t,n,i,e)}finally{if(s)try{ee.closeSync(i)}catch{}}},pf=(r,e,t,s,i)=>{let n=new ga.WriteStreamSync(r.file,{fd:s,start:t});e.pipe(n),df(e,i)},mf=(r,e,t)=>{e=Array.from(e);let s=new Da(r),i=(o,a,l)=>{let c=(S,P)=>{S?ee.close(o,k=>l(S)):l(null,P)},h=0;if(a===0)return c(null,0);let d=0,g=Buffer.alloc(512),w=(S,P)=>{if(S)return c(S);if(d+=P,d<512&&P)return ee.read(o,g,d,g.length-d,h+d,w);if(h===0&&g[0]===31&&g[1]===139)return c(new Error("cannot append to compressed archives"));if(d<512)return c(null,h);let k=new Aa(g);if(!k.cksumValid)return c(null,h);let O=512*Math.ceil(k.size/512);if(h+O+512>a||(h+=O+512,h>=a))return c(null,h);r.mtimeCache&&r.mtimeCache.set(k.path,k.mtime),d=0,ee.read(o,g,0,512,h,w)};ee.read(o,g,0,512,h,w)},n=new Promise((o,a)=>{s.on("error",a);let l="r+",c=(h,d)=>{if(h&&h.code==="ENOENT"&&l==="r+")return l="w+",ee.open(r.file,l,c);if(h)return a(h);ee.fstat(d,(g,w)=>{if(g)return ee.close(d,()=>a(g));i(d,w.size,(S,P)=>{if(S)return a(S);let k=new ga.WriteStream(r.file,{fd:d,start:P});s.pipe(k),k.on("error",a),k.on("close",o),Ca(s,e)})})};ee.open(r.file,l,c)});return t?n.then(t,t):n},df=(r,e)=>{e.forEach(t=>{t.charAt(0)==="@"?Ea({file:ya.resolve(r.cwd,t.slice(1)),sync:!0,noResume:!0,onentry:s=>r.add(s)}):r.add(t)}),r.end()},Ca=(r,e)=>{for(;e.length;){let t=e.shift();if(t.charAt(0)==="@")return Ea({file:ya.resolve(r.cwd,t.slice(1)),noResume:!0,onentry:s=>r.add(s)}).then(s=>Ca(r,e));r.add(t)}r.end()}});var ba=y((g0,Fa)=>{"use strict";u();var Df=vt(),gf=Ln();Fa.exports=(r,e,t)=>{let s=Df(r);if(!s.file)throw new TypeError("file is required");if(s.gzip)throw new TypeError("cannot append to compressed archives");if(!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");return e=Array.from(e),Ef(s),gf(s,e,t)};var Ef=r=>{let e=r.filter;r.mtimeCache||(r.mtimeCache=new Map),r.filter=e?(t,s)=>e(t,s)&&!(r.mtimeCache.get(t)>s.mtime):(t,s)=>!(r.mtimeCache.get(t)>s.mtime)}});var _a=y((E0,ka)=>{u();var{promisify:Sa}=A("util"),et=A("fs"),yf=r=>{if(!r)r={mode:511,fs:et};else if(typeof r=="object")r={mode:511,fs:et,...r};else if(typeof r=="number")r={mode:r,fs:et};else if(typeof r=="string")r={mode:parseInt(r,8),fs:et};else throw new TypeError("invalid options argument");return r.mkdir=r.mkdir||r.fs.mkdir||et.mkdir,r.mkdirAsync=Sa(r.mkdir),r.stat=r.stat||r.fs.stat||et.stat,r.statAsync=Sa(r.stat),r.statSync=r.statSync||r.fs.statSync||et.statSync,r.mkdirSync=r.mkdirSync||r.fs.mkdirSync||et.mkdirSync,r};ka.exports=yf});var Ba=y((y0,Ra)=>{u();var Af=process.env.__TESTING_MKDIRP_PLATFORM__||process.platform,{resolve:Cf,parse:wf}=A("path"),Ff=r=>{if(/\0/.test(r))throw Object.assign(new TypeError("path must be a string without null bytes"),{path:r,code:"ERR_INVALID_ARG_VALUE"});if(r=Cf(r),Af==="win32"){let e=/[*|"<>?:]/,{root:t}=wf(r);if(e.test(r.substr(t.length)))throw Object.assign(new Error("Illegal characters in path."),{path:r,code:"EINVAL"})}return r};Ra.exports=Ff});var Pa=y((A0,Ta)=>{u();var{dirname:va}=A("path"),xa=(r,e,t=void 0)=>t===e?Promise.resolve():r.statAsync(e).then(s=>s.isDirectory()?t:void 0,s=>s.code==="ENOENT"?xa(r,va(e),e):void 0),Oa=(r,e,t=void 0)=>{if(t!==e)try{return r.statSync(e).isDirectory()?t:void 0}catch(s){return s.code==="ENOENT"?Oa(r,va(e),e):void 0}};Ta.exports={findMade:xa,findMadeSync:Oa}});var jn=y((C0,La)=>{u();var{dirname:Na}=A("path"),Mn=(r,e,t)=>{e.recursive=!1;let s=Na(r);return s===r?e.mkdirAsync(r,e).catch(i=>{if(i.code!=="EISDIR")throw i}):e.mkdirAsync(r,e).then(()=>t||r,i=>{if(i.code==="ENOENT")return Mn(s,e).then(n=>Mn(r,e,n));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;return e.statAsync(r).then(n=>{if(n.isDirectory())return t;throw i},()=>{throw i})})},In=(r,e,t)=>{let s=Na(r);if(e.recursive=!1,s===r)try{return e.mkdirSync(r,e)}catch(i){if(i.code!=="EISDIR")throw i;return}try{return e.mkdirSync(r,e),t||r}catch(i){if(i.code==="ENOENT")return In(r,e,In(s,e,t));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;try{if(!e.statSync(r).isDirectory())throw i}catch{throw i}}};La.exports={mkdirpManual:Mn,mkdirpManualSync:In}});var ja=y((w0,Ia)=>{u();var{dirname:Ma}=A("path"),{findMade:bf,findMadeSync:Sf}=Pa(),{mkdirpManual:kf,mkdirpManualSync:_f}=jn(),Rf=(r,e)=>(e.recursive=!0,Ma(r)===r?e.mkdirAsync(r,e):bf(e,r).then(s=>e.mkdirAsync(r,e).then(()=>s).catch(i=>{if(i.code==="ENOENT")return kf(r,e);throw i}))),Bf=(r,e)=>{if(e.recursive=!0,Ma(r)===r)return e.mkdirSync(r,e);let s=Sf(e,r);try{return e.mkdirSync(r,e),s}catch(i){if(i.code==="ENOENT")return _f(r,e);throw i}};Ia.exports={mkdirpNative:Rf,mkdirpNativeSync:Bf}});var za=y((F0,$a)=>{u();var qa=A("fs"),vf=process.env.__TESTING_MKDIRP_NODE_VERSION__||process.version,qn=vf.replace(/^v/,"").split("."),Ua=+qn[0]>10||+qn[0]==10&&+qn[1]>=12,xf=Ua?r=>r.mkdir===qa.mkdir:()=>!1,Of=Ua?r=>r.mkdirSync===qa.mkdirSync:()=>!1;$a.exports={useNative:xf,useNativeSync:Of}});var Ya=y((b0,Va)=>{u();var Yt=_a(),Kt=Ba(),{mkdirpNative:Wa,mkdirpNativeSync:Ga}=ja(),{mkdirpManual:Ha,mkdirpManualSync:Ja}=jn(),{useNative:Tf,useNativeSync:Pf}=za(),Zt=(r,e)=>(r=Kt(r),e=Yt(e),Tf(e)?Wa(r,e):Ha(r,e)),Nf=(r,e)=>(r=Kt(r),e=Yt(e),Pf(e)?Ga(r,e):Ja(r,e));Zt.sync=Nf;Zt.native=(r,e)=>Wa(Kt(r),Yt(e));Zt.manual=(r,e)=>Ha(Kt(r),Yt(e));Zt.nativeSync=(r,e)=>Ga(Kt(r),Yt(e));Zt.manualSync=(r,e)=>Ja(Kt(r),Yt(e));Va.exports=Zt});var rc=y((S0,tc)=>{"use strict";u();var ne=A("fs"),wt=A("path"),Lf=ne.lchown?"lchown":"chown",Mf=ne.lchownSync?"lchownSync":"chownSync",Za=ne.lchown&&!process.version.match(/v1[1-9]+\./)&&!process.version.match(/v10\.[6-9]/),Ka=(r,e,t)=>{try{return ne[Mf](r,e,t)}catch(s){if(s.code!=="ENOENT")throw s}},If=(r,e,t)=>{try{return ne.chownSync(r,e,t)}catch(s){if(s.code!=="ENOENT")throw s}},jf=Za?(r,e,t,s)=>i=>{!i||i.code!=="EISDIR"?s(i):ne.chown(r,e,t,s)}:(r,e,t,s)=>s,Un=Za?(r,e,t)=>{try{return Ka(r,e,t)}catch(s){if(s.code!=="EISDIR")throw s;If(r,e,t)}}:(r,e,t)=>Ka(r,e,t),qf=process.version,Xa=(r,e,t)=>ne.readdir(r,e,t),Uf=(r,e)=>ne.readdirSync(r,e);/^v4\./.test(qf)&&(Xa=(r,e,t)=>ne.readdir(r,t));var Ps=(r,e,t,s)=>{ne[Lf](r,e,t,jf(r,e,t,i=>{s(i&&i.code!=="ENOENT"?i:null)}))},Qa=(r,e,t,s,i)=>{if(typeof e=="string")return ne.lstat(wt.resolve(r,e),(n,o)=>{if(n)return i(n.code!=="ENOENT"?n:null);o.name=e,Qa(r,o,t,s,i)});if(e.isDirectory())$n(wt.resolve(r,e.name),t,s,n=>{if(n)return i(n);let o=wt.resolve(r,e.name);Ps(o,t,s,i)});else{let n=wt.resolve(r,e.name);Ps(n,t,s,i)}},$n=(r,e,t,s)=>{Xa(r,{withFileTypes:!0},(i,n)=>{if(i){if(i.code==="ENOENT")return s();if(i.code!=="ENOTDIR"&&i.code!=="ENOTSUP")return s(i)}if(i||!n.length)return Ps(r,e,t,s);let o=n.length,a=null,l=c=>{if(!a){if(c)return s(a=c);if(--o===0)return Ps(r,e,t,s)}};n.forEach(c=>Qa(r,c,e,t,l))})},$f=(r,e,t,s)=>{if(typeof e=="string")try{let i=ne.lstatSync(wt.resolve(r,e));i.name=e,e=i}catch(i){if(i.code==="ENOENT")return;throw i}e.isDirectory()&&ec(wt.resolve(r,e.name),t,s),Un(wt.resolve(r,e.name),t,s)},ec=(r,e,t)=>{let s;try{s=Uf(r,{withFileTypes:!0})}catch(i){if(i.code==="ENOENT")return;if(i.code==="ENOTDIR"||i.code==="ENOTSUP")return Un(r,e,t);throw i}return s&&s.length&&s.forEach(i=>$f(r,i,e,t)),Un(r,e,t)};tc.exports=$n;$n.sync=ec});var oc=y((k0,zn)=>{"use strict";u();var sc=Ya(),oe=A("fs"),Ns=A("path"),ic=rc(),pe=Nt(),Ls=class extends Error{constructor(e,t){super("Cannot extract through symbolic link"),this.path=t,this.symlink=e}get name(){return"SylinkError"}},Ms=class extends Error{constructor(e,t){super(t+": Cannot cd into '"+e+"'"),this.path=e,this.code=t}get name(){return"CwdError"}},Is=(r,e)=>r.get(pe(e)),Fr=(r,e,t)=>r.set(pe(e),t),zf=(r,e)=>{oe.stat(r,(t,s)=>{(t||!s.isDirectory())&&(t=new Ms(r,t&&t.code||"ENOTDIR")),e(t)})};zn.exports=(r,e,t)=>{r=pe(r);let s=e.umask,i=e.mode|448,n=(i&s)!==0,o=e.uid,a=e.gid,l=typeof o=="number"&&typeof a=="number"&&(o!==e.processUid||a!==e.processGid),c=e.preserve,h=e.unlink,d=e.cache,g=pe(e.cwd),w=(k,O)=>{k?t(k):(Fr(d,r,!0),O&&l?ic(O,o,a,ut=>w(ut)):n?oe.chmod(r,i,t):t())};if(d&&Is(d,r)===!0)return w();if(r===g)return zf(r,w);if(c)return sc(r,{mode:i}).then(k=>w(null,k),w);let P=pe(Ns.relative(g,r)).split("/");js(g,P,i,d,h,g,null,w)};var js=(r,e,t,s,i,n,o,a)=>{if(!e.length)return a(null,o);let l=e.shift(),c=pe(Ns.resolve(r+"/"+l));if(Is(s,c))return js(c,e,t,s,i,n,o,a);oe.mkdir(c,t,nc(c,e,t,s,i,n,o,a))},nc=(r,e,t,s,i,n,o,a)=>l=>{l?oe.lstat(r,(c,h)=>{if(c)c.path=c.path&&pe(c.path),a(c);else if(h.isDirectory())js(r,e,t,s,i,n,o,a);else if(i)oe.unlink(r,d=>{if(d)return a(d);oe.mkdir(r,t,nc(r,e,t,s,i,n,o,a))});else{if(h.isSymbolicLink())return a(new Ls(r,r+"/"+e.join("/")));a(l)}}):(o=o||r,js(r,e,t,s,i,n,o,a))},Wf=r=>{let e=!1,t="ENOTDIR";try{e=oe.statSync(r).isDirectory()}catch(s){t=s.code}finally{if(!e)throw new Ms(r,t)}};zn.exports.sync=(r,e)=>{r=pe(r);let t=e.umask,s=e.mode|448,i=(s&t)!==0,n=e.uid,o=e.gid,a=typeof n=="number"&&typeof o=="number"&&(n!==e.processUid||o!==e.processGid),l=e.preserve,c=e.unlink,h=e.cache,d=pe(e.cwd),g=k=>{Fr(h,r,!0),k&&a&&ic.sync(k,n,o),i&&oe.chmodSync(r,s)};if(h&&Is(h,r)===!0)return g();if(r===d)return Wf(d),g();if(l)return g(sc.sync(r,s));let S=pe(Ns.relative(d,r)).split("/"),P=null;for(let k=S.shift(),O=d;k&&(O+="/"+k);k=S.shift())if(O=pe(Ns.resolve(O)),!Is(h,O))try{oe.mkdirSync(O,s),P=P||O,Fr(h,O,!0)}catch{let me=oe.lstatSync(O);if(me.isDirectory()){Fr(h,O,!0);continue}else if(c){oe.unlinkSync(O),oe.mkdirSync(O,s),P=P||O,Fr(h,O,!0);continue}else if(me.isSymbolicLink())return new Ls(O,O+"/"+S.join("/"))}return g(P)}});var Gn=y((_0,uc)=>{u();var Wn=Object.create(null),{hasOwnProperty:Gf}=Object.prototype;uc.exports=r=>(Gf.call(Wn,r)||(Wn[r]=r.normalize("NFKD")),Wn[r])});var hc=y((R0,lc)=>{u();var ac=A("assert"),Hf=Gn(),Jf=It(),{join:cc}=A("path"),Vf=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform,Yf=Vf==="win32";lc.exports=()=>{let r=new Map,e=new Map,t=c=>c.split("/").slice(0,-1).reduce((d,g)=>(d.length&&(g=cc(d[d.length-1],g)),d.push(g||"/"),d),[]),s=new Set,i=c=>{let h=e.get(c);if(!h)throw new Error("function does not have any path reservations");return{paths:h.paths.map(d=>r.get(d)),dirs:[...h.dirs].map(d=>r.get(d))}},n=c=>{let{paths:h,dirs:d}=i(c);return h.every(g=>g[0]===c)&&d.every(g=>g[0]instanceof Set&&g[0].has(c))},o=c=>s.has(c)||!n(c)?!1:(s.add(c),c(()=>a(c)),!0),a=c=>{if(!s.has(c))return!1;let{paths:h,dirs:d}=e.get(c),g=new Set;return h.forEach(w=>{let S=r.get(w);ac.equal(S[0],c),S.length===1?r.delete(w):(S.shift(),typeof S[0]=="function"?g.add(S[0]):S[0].forEach(P=>g.add(P)))}),d.forEach(w=>{let S=r.get(w);ac(S[0]instanceof Set),S[0].size===1&&S.length===1?r.delete(w):S[0].size===1?(S.shift(),g.add(S[0])):S[0].delete(c)}),s.delete(c),g.forEach(w=>o(w)),!0};return{check:n,reserve:(c,h)=>{c=Yf?["win32 parallelization disabled"]:c.map(g=>Hf(Jf(cc(g))).toLowerCase());let d=new Set(c.map(g=>t(g)).reduce((g,w)=>g.concat(w)));return e.set(h,{dirs:d,paths:c}),c.forEach(g=>{let w=r.get(g);w?w.push(h):r.set(g,[h])}),d.forEach(g=>{let w=r.get(g);w?w[w.length-1]instanceof Set?w[w.length-1].add(h):w.push(new Set([h])):r.set(g,[new Set([h])])}),o(h)}}}});var mc=y((B0,pc)=>{u();var Kf=process.env.__FAKE_PLATFORM__||process.platform,Zf=Kf==="win32",Xf=global.__FAKE_TESTING_FS__||A("fs"),{O_CREAT:Qf,O_TRUNC:ep,O_WRONLY:tp,UV_FS_O_FILEMAP:fc=0}=Xf.constants,rp=Zf&&!!fc,sp=512*1024,ip=fc|ep|Qf|tp;pc.exports=rp?r=>r<sp?ip:"w":()=>"w"});var eo=y((v0,Rc)=>{"use strict";u();var np=A("assert"),op=xs(),R=A("fs"),up=Jt(),Ie=A("path"),Sc=oc(),dc=Xi(),ap=hc(),cp=Qi(),te=Nt(),lp=It(),hp=Gn(),Dc=Symbol("onEntry"),Vn=Symbol("checkFs"),gc=Symbol("checkFs2"),$s=Symbol("pruneCache"),Yn=Symbol("isReusable"),ue=Symbol("makeFs"),Kn=Symbol("file"),Zn=Symbol("directory"),zs=Symbol("link"),Ec=Symbol("symlink"),yc=Symbol("hardlink"),Ac=Symbol("unsupported"),Cc=Symbol("checkPath"),tt=Symbol("mkdir"),G=Symbol("onError"),qs=Symbol("pending"),wc=Symbol("pend"),Xt=Symbol("unpend"),Hn=Symbol("ended"),Jn=Symbol("maybeClose"),Xn=Symbol("skip"),br=Symbol("doChown"),Sr=Symbol("uid"),kr=Symbol("gid"),_r=Symbol("checkedCwd"),kc=A("crypto"),_c=mc(),fp=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform,Rr=fp==="win32",pp=(r,e)=>{if(!Rr)return R.unlink(r,e);let t=r+".DELETE."+kc.randomBytes(16).toString("hex");R.rename(r,t,s=>{if(s)return e(s);R.unlink(t,e)})},mp=r=>{if(!Rr)return R.unlinkSync(r);let e=r+".DELETE."+kc.randomBytes(16).toString("hex");R.renameSync(r,e),R.unlinkSync(e)},Fc=(r,e,t)=>r===r>>>0?r:e===e>>>0?e:t,bc=r=>hp(lp(te(r))).toLowerCase(),dp=(r,e)=>{e=bc(e);for(let t of r.keys()){let s=bc(t);(s===e||s.indexOf(e+"/")===0)&&r.delete(t)}},Dp=r=>{for(let e of r.keys())r.delete(e)},Br=class extends op{constructor(e){if(e||(e={}),e.ondone=t=>{this[Hn]=!0,this[Jn]()},super(e),this[_r]=!1,this.reservations=ap(),this.transform=typeof e.transform=="function"?e.transform:null,this.writable=!0,this.readable=!1,this[qs]=0,this[Hn]=!1,this.dirCache=e.dirCache||new Map,typeof e.uid=="number"||typeof e.gid=="number"){if(typeof e.uid!="number"||typeof e.gid!="number")throw new TypeError("cannot set owner without number uid and gid");if(e.preserveOwner)throw new TypeError("cannot preserve owner in archive and also set owner explicitly");this.uid=e.uid,this.gid=e.gid,this.setOwner=!0}else this.uid=null,this.gid=null,this.setOwner=!1;e.preserveOwner===void 0&&typeof e.uid!="number"?this.preserveOwner=process.getuid&&process.getuid()===0:this.preserveOwner=!!e.preserveOwner,this.processUid=(this.preserveOwner||this.setOwner)&&process.getuid?process.getuid():null,this.processGid=(this.preserveOwner||this.setOwner)&&process.getgid?process.getgid():null,this.forceChown=e.forceChown===!0,this.win32=!!e.win32||Rr,this.newer=!!e.newer,this.keep=!!e.keep,this.noMtime=!!e.noMtime,this.preservePaths=!!e.preservePaths,this.unlink=!!e.unlink,this.cwd=te(Ie.resolve(e.cwd||process.cwd())),this.strip=+e.strip||0,this.processUmask=e.noChmod?0:process.umask(),this.umask=typeof e.umask=="number"?e.umask:this.processUmask,this.dmode=e.dmode||511&~this.umask,this.fmode=e.fmode||438&~this.umask,this.on("entry",t=>this[Dc](t))}warn(e,t,s={}){return(e==="TAR_BAD_ARCHIVE"||e==="TAR_ABORT")&&(s.recoverable=!1),super.warn(e,t,s)}[Jn](){this[Hn]&&this[qs]===0&&(this.emit("prefinish"),this.emit("finish"),this.emit("end"))}[Cc](e){if(this.strip){let t=te(e.path).split("/");if(t.length<this.strip)return!1;if(e.path=t.slice(this.strip).join("/"),e.type==="Link"){let s=te(e.linkpath).split("/");if(s.length>=this.strip)e.linkpath=s.slice(this.strip).join("/");else return!1}}if(!this.preservePaths){let t=te(e.path),s=t.split("/");if(s.includes("..")||Rr&&/^[a-z]:\.\.$/i.test(s[0]))return this.warn("TAR_ENTRY_ERROR","path contains '..'",{entry:e,path:t}),!1;let[i,n]=cp(t);i&&(e.path=n,this.warn("TAR_ENTRY_INFO",`stripping ${i} from absolute path`,{entry:e,path:t}))}if(Ie.isAbsolute(e.path)?e.absolute=te(Ie.resolve(e.path)):e.absolute=te(Ie.resolve(this.cwd,e.path)),!this.preservePaths&&e.absolute.indexOf(this.cwd+"/")!==0&&e.absolute!==this.cwd)return this.warn("TAR_ENTRY_ERROR","path escaped extraction target",{entry:e,path:te(e.path),resolvedPath:e.absolute,cwd:this.cwd}),!1;if(e.absolute===this.cwd&&e.type!=="Directory"&&e.type!=="GNUDumpDir")return!1;if(this.win32){let{root:t}=Ie.win32.parse(e.absolute);e.absolute=t+dc.encode(e.absolute.slice(t.length));let{root:s}=Ie.win32.parse(e.path);e.path=s+dc.encode(e.path.slice(s.length))}return!0}[Dc](e){if(!this[Cc](e))return e.resume();switch(np.equal(typeof e.absolute,"string"),e.type){case"Directory":case"GNUDumpDir":e.mode&&(e.mode=e.mode|448);case"File":case"OldFile":case"ContiguousFile":case"Link":case"SymbolicLink":return this[Vn](e);case"CharacterDevice":case"BlockDevice":case"FIFO":default:return this[Ac](e)}}[G](e,t){e.name==="CwdError"?this.emit("error",e):(this.warn("TAR_ENTRY_ERROR",e,{entry:t}),this[Xt](),t.resume())}[tt](e,t,s){Sc(te(e),{uid:this.uid,gid:this.gid,processUid:this.processUid,processGid:this.processGid,umask:this.processUmask,preserve:this.preservePaths,unlink:this.unlink,cache:this.dirCache,cwd:this.cwd,mode:t,noChmod:this.noChmod},s)}[br](e){return this.forceChown||this.preserveOwner&&(typeof e.uid=="number"&&e.uid!==this.processUid||typeof e.gid=="number"&&e.gid!==this.processGid)||typeof this.uid=="number"&&this.uid!==this.processUid||typeof this.gid=="number"&&this.gid!==this.processGid}[Sr](e){return Fc(this.uid,e.uid,this.processUid)}[kr](e){return Fc(this.gid,e.gid,this.processGid)}[Kn](e,t){let s=e.mode&4095||this.fmode,i=new up.WriteStream(e.absolute,{flags:_c(e.size),mode:s,autoClose:!1});i.on("error",l=>{i.fd&&R.close(i.fd,()=>{}),i.write=()=>!0,this[G](l,e),t()});let n=1,o=l=>{if(l){i.fd&&R.close(i.fd,()=>{}),this[G](l,e),t();return}--n===0&&R.close(i.fd,c=>{c?this[G](c,e):this[Xt](),t()})};i.on("finish",l=>{let c=e.absolute,h=i.fd;if(e.mtime&&!this.noMtime){n++;let d=e.atime||new Date,g=e.mtime;R.futimes(h,d,g,w=>w?R.utimes(c,d,g,S=>o(S&&w)):o())}if(this[br](e)){n++;let d=this[Sr](e),g=this[kr](e);R.fchown(h,d,g,w=>w?R.chown(c,d,g,S=>o(S&&w)):o())}o()});let a=this.transform&&this.transform(e)||e;a!==e&&(a.on("error",l=>{this[G](l,e),t()}),e.pipe(a)),a.pipe(i)}[Zn](e,t){let s=e.mode&4095||this.dmode;this[tt](e.absolute,s,i=>{if(i){this[G](i,e),t();return}let n=1,o=a=>{--n===0&&(t(),this[Xt](),e.resume())};e.mtime&&!this.noMtime&&(n++,R.utimes(e.absolute,e.atime||new Date,e.mtime,o)),this[br](e)&&(n++,R.chown(e.absolute,this[Sr](e),this[kr](e),o)),o()})}[Ac](e){e.unsupported=!0,this.warn("TAR_ENTRY_UNSUPPORTED",`unsupported entry type: ${e.type}`,{entry:e}),e.resume()}[Ec](e,t){this[zs](e,e.linkpath,"symlink",t)}[yc](e,t){let s=te(Ie.resolve(this.cwd,e.linkpath));this[zs](e,s,"link",t)}[wc](){this[qs]++}[Xt](){this[qs]--,this[Jn]()}[Xn](e){this[Xt](),e.resume()}[Yn](e,t){return e.type==="File"&&!this.unlink&&t.isFile()&&t.nlink<=1&&!Rr}[Vn](e){this[wc]();let t=[e.path];e.linkpath&&t.push(e.linkpath),this.reservations.reserve(t,s=>this[gc](e,s))}[$s](e){e.type==="SymbolicLink"?Dp(this.dirCache):e.type!=="Directory"&&dp(this.dirCache,e.absolute)}[gc](e,t){this[$s](e);let s=a=>{this[$s](e),t(a)},i=()=>{this[tt](this.cwd,this.dmode,a=>{if(a){this[G](a,e),s();return}this[_r]=!0,n()})},n=()=>{if(e.absolute!==this.cwd){let a=te(Ie.dirname(e.absolute));if(a!==this.cwd)return this[tt](a,this.dmode,l=>{if(l){this[G](l,e),s();return}o()})}o()},o=()=>{R.lstat(e.absolute,(a,l)=>{if(l&&(this.keep||this.newer&&l.mtime>e.mtime)){this[Xn](e),s();return}if(a||this[Yn](e,l))return this[ue](null,e,s);if(l.isDirectory()){if(e.type==="Directory"){let c=!this.noChmod&&e.mode&&(l.mode&4095)!==e.mode,h=d=>this[ue](d,e,s);return c?R.chmod(e.absolute,e.mode,h):h()}if(e.absolute!==this.cwd)return R.rmdir(e.absolute,c=>this[ue](c,e,s))}if(e.absolute===this.cwd)return this[ue](null,e,s);pp(e.absolute,c=>this[ue](c,e,s))})};this[_r]?n():i()}[ue](e,t,s){if(e){this[G](e,t),s();return}switch(t.type){case"File":case"OldFile":case"ContiguousFile":return this[Kn](t,s);case"Link":return this[yc](t,s);case"SymbolicLink":return this[Ec](t,s);case"Directory":case"GNUDumpDir":return this[Zn](t,s)}}[zs](e,t,s,i){R[s](t,e.absolute,n=>{n?this[G](n,e):(this[Xt](),e.resume()),i()})}},Us=r=>{try{return[null,r()]}catch(e){return[e,null]}},Qn=class extends Br{[ue](e,t){return super[ue](e,t,()=>{})}[Vn](e){if(this[$s](e),!this[_r]){let n=this[tt](this.cwd,this.dmode);if(n)return this[G](n,e);this[_r]=!0}if(e.absolute!==this.cwd){let n=te(Ie.dirname(e.absolute));if(n!==this.cwd){let o=this[tt](n,this.dmode);if(o)return this[G](o,e)}}let[t,s]=Us(()=>R.lstatSync(e.absolute));if(s&&(this.keep||this.newer&&s.mtime>e.mtime))return this[Xn](e);if(t||this[Yn](e,s))return this[ue](null,e);if(s.isDirectory()){if(e.type==="Directory"){let o=!this.noChmod&&e.mode&&(s.mode&4095)!==e.mode,[a]=o?Us(()=>{R.chmodSync(e.absolute,e.mode)}):[];return this[ue](a,e)}let[n]=Us(()=>R.rmdirSync(e.absolute));this[ue](n,e)}let[i]=e.absolute===this.cwd?[]:Us(()=>mp(e.absolute));this[ue](i,e)}[Kn](e,t){let s=e.mode&4095||this.fmode,i=a=>{let l;try{R.closeSync(n)}catch(c){l=c}(a||l)&&this[G](a||l,e),t()},n;try{n=R.openSync(e.absolute,_c(e.size),s)}catch(a){return i(a)}let o=this.transform&&this.transform(e)||e;o!==e&&(o.on("error",a=>this[G](a,e)),e.pipe(o)),o.on("data",a=>{try{R.writeSync(n,a,0,a.length)}catch(l){i(l)}}),o.on("end",a=>{let l=null;if(e.mtime&&!this.noMtime){let c=e.atime||new Date,h=e.mtime;try{R.futimesSync(n,c,h)}catch(d){try{R.utimesSync(e.absolute,c,h)}catch{l=d}}}if(this[br](e)){let c=this[Sr](e),h=this[kr](e);try{R.fchownSync(n,c,h)}catch(d){try{R.chownSync(e.absolute,c,h)}catch{l=l||d}}}i(l)})}[Zn](e,t){let s=e.mode&4095||this.dmode,i=this[tt](e.absolute,s);if(i){this[G](i,e),t();return}if(e.mtime&&!this.noMtime)try{R.utimesSync(e.absolute,e.atime||new Date,e.mtime)}catch{}if(this[br](e))try{R.chownSync(e.absolute,this[Sr](e),this[kr](e))}catch{}t(),e.resume()}[tt](e,t){try{return Sc.sync(te(e),{uid:this.uid,gid:this.gid,processUid:this.processUid,processGid:this.processGid,umask:this.processUmask,preserve:this.preservePaths,unlink:this.unlink,cache:this.dirCache,cwd:this.cwd,mode:t})}catch(s){return s}}[zs](e,t,s,i){try{R[s+"Sync"](t,e.absolute),i(),e.resume()}catch(n){return this[G](n,e)}}};Br.Sync=Qn;Rc.exports=Br});var Tc=y((x0,Oc)=>{"use strict";u();var gp=vt(),Ws=eo(),vc=A("fs"),xc=Jt(),Bc=A("path"),to=It();Oc.exports=(r,e,t)=>{typeof r=="function"?(t=r,e=null,r={}):Array.isArray(r)&&(e=r,r={}),typeof e=="function"&&(t=e,e=null),e?e=Array.from(e):e=[];let s=gp(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return e.length&&Ep(s,e),s.file&&s.sync?yp(s):s.file?Ap(s,t):s.sync?Cp(s):wp(s)};var Ep=(r,e)=>{let t=new Map(e.map(n=>[to(n),!0])),s=r.filter,i=(n,o)=>{let a=o||Bc.parse(n).root||".",l=n===a?!1:t.has(n)?t.get(n):i(Bc.dirname(n),a);return t.set(n,l),l};r.filter=s?(n,o)=>s(n,o)&&i(to(n)):n=>i(to(n))},yp=r=>{let e=new Ws.Sync(r),t=r.file,s=vc.statSync(t),i=r.maxReadSize||16*1024*1024;new xc.ReadStreamSync(t,{readSize:i,size:s.size}).pipe(e)},Ap=(r,e)=>{let t=new Ws(r),s=r.maxReadSize||16*1024*1024,i=r.file,n=new Promise((o,a)=>{t.on("error",a),t.on("close",o),vc.stat(i,(l,c)=>{if(l)a(l);else{let h=new xc.ReadStream(i,{readSize:s,size:c.size});h.on("error",a),h.pipe(t)}})});return e?n.then(e,e):n},Cp=r=>new Ws.Sync(r),wp=r=>new Ws(r)});var Pc=y(M=>{"use strict";u();M.c=M.create=da();M.r=M.replace=Ln();M.t=M.list=Os();M.u=M.update=ba();M.x=M.extract=Tc();M.Pack=gs();M.Unpack=eo();M.Parse=xs();M.ReadEntry=es();M.WriteEntry=ln();M.Header=Mt();M.Pax=rs();M.types=Gi()});var Lc=y((q0,Nc)=>{u();function ae(r,e){typeof e=="boolean"&&(e={forever:e}),this._originalTimeouts=JSON.parse(JSON.stringify(r)),this._timeouts=r,this._options=e||{},this._maxRetryTime=e&&e.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}Nc.exports=ae;ae.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)};ae.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null};ae.prototype.retry=function(r){if(this._timeout&&clearTimeout(this._timeout),!r)return!1;var e=new Date().getTime();if(r&&e-this._operationStart>=this._maxRetryTime)return this._errors.push(r),this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(r);var t=this._timeouts.shift();if(t===void 0)if(this._cachedTimeouts)this._errors.splice(0,this._errors.length-1),t=this._cachedTimeouts.slice(-1);else return!1;var s=this;return this._timer=setTimeout(function(){s._attempts++,s._operationTimeoutCb&&(s._timeout=setTimeout(function(){s._operationTimeoutCb(s._attempts)},s._operationTimeout),s._options.unref&&s._timeout.unref()),s._fn(s._attempts)},t),this._options.unref&&this._timer.unref(),!0};ae.prototype.attempt=function(r,e){this._fn=r,e&&(e.timeout&&(this._operationTimeout=e.timeout),e.cb&&(this._operationTimeoutCb=e.cb));var t=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){t._operationTimeoutCb()},t._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)};ae.prototype.try=function(r){console.log("Using RetryOperation.try() is deprecated"),this.attempt(r)};ae.prototype.start=function(r){console.log("Using RetryOperation.start() is deprecated"),this.attempt(r)};ae.prototype.start=ae.prototype.try;ae.prototype.errors=function(){return this._errors};ae.prototype.attempts=function(){return this._attempts};ae.prototype.mainError=function(){if(this._errors.length===0)return null;for(var r={},e=null,t=0,s=0;s<this._errors.length;s++){var i=this._errors[s],n=i.message,o=(r[n]||0)+1;r[n]=o,o>=t&&(e=i,t=o)}return e}});var Mc=y(Ft=>{u();var kp=Lc();Ft.operation=function(r){var e=Ft.timeouts(r);return new kp(e,{forever:r&&(r.forever||r.retries===1/0),unref:r&&r.unref,maxRetryTime:r&&r.maxRetryTime})};Ft.timeouts=function(r){if(r instanceof Array)return[].concat(r);var e={retries:10,factor:2,minTimeout:1*1e3,maxTimeout:1/0,randomize:!1};for(var t in r)e[t]=r[t];if(e.minTimeout>e.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var s=[],i=0;i<e.retries;i++)s.push(this.createTimeout(i,e));return r&&r.forever&&!s.length&&s.push(this.createTimeout(i,e)),s.sort(function(n,o){return n-o}),s};Ft.createTimeout=function(r,e){var t=e.randomize?Math.random()+1:1,s=Math.round(t*Math.max(e.minTimeout,1)*Math.pow(e.factor,r));return s=Math.min(s,e.maxTimeout),s};Ft.wrap=function(r,e,t){if(e instanceof Array&&(t=e,e=null),!t){t=[];for(var s in r)typeof r[s]=="function"&&t.push(s)}for(var i=0;i<t.length;i++){var n=t[i],o=r[n];r[n]=function(l){var c=Ft.operation(e),h=Array.prototype.slice.call(arguments,1),d=h.pop();h.push(function(g){c.retry(g)||(g&&(arguments[0]=c.mainError()),d.apply(this,arguments))}),c.attempt(function(){l.apply(r,h)})}.bind(r,o),r[n].options=e}}});var jc=y(($0,Ic)=>{u();Ic.exports=Mc()});var Uc=y((z0,qc)=>{u();var _p=jc();function Rp(r,e){function t(s,i){var n=e||{},o;"randomize"in n||(n.randomize=!0),o=_p.operation(n);function a(h){i(h||new Error("Aborted"))}function l(h,d){if(h.bail){a(h);return}o.retry(h)?n.onRetry&&n.onRetry(h,d):i(o.mainError())}function c(h){var d;try{d=r(a,h)}catch(g){l(g,h);return}Promise.resolve(d).then(s).catch(function(w){l(w,h)})}o.attempt(c)}return new Promise(t)}qc.exports=Rp});var Pr={};cl(Pr,{bold:()=>Al,dimmed:()=>wl,error:()=>ri,grey:()=>Fl,info:()=>yl,item:()=>bl,log:()=>Ue,turboBlue:()=>_t,turboGradient:()=>El,turboLoader:()=>lo,turboRed:()=>ao,underline:()=>Cl,warn:()=>ho,yellow:()=>co});u();import{reset as hl,bold as ur,underline as fl,gray as pl,dim as ml}from"picocolors";import dl from"ora";import Dl from"gradient-string";var oo="#0099F7",uo="#F11712",gl="#FFFF00",ti=r=>{let e=Sl(r);return t=>`\x1B[38;5;${e}m${t}${hl("")}`},El=Dl(oo,uo),_t=ti(oo),ao=ti(uo),co=ti(gl),lo=r=>dl({text:r,spinner:{frames:["   ",_t(">  "),_t(">> "),_t(">>>")]}}),yl=(...r)=>{Ue(_t(ur(">>>")),r.join(" "))},Al=(...r)=>{Ue(ur(r.join(" ")))},Cl=(...r)=>{Ue(fl(r.join(" ")))},wl=(...r)=>{Ue(ml(r.join(" ")))},Fl=(...r)=>{Ue(pl(r.join(" ")))},bl=(...r)=>{Ue(_t(ur("  \u2022")),r.join(" "))},Ue=(...r)=>{console.log(...r)},ho=(...r)=>{console.error(co(ur(">>>")),r.join(" "))},ri=(...r)=>{console.error(ao(ur(">>>")),r.join(" "))};function Sl(r){let e=parseInt(r.slice(1),16),t=Math.floor(e/(256*256))%256,s=Math.floor(e/256)%256,i=e%256;return 16+36*Math.round(t/255*5)+6*Math.round(s/255*5)+Math.round(i/255*5)}u();import kl from"os";import _l from"execa";async function Nr(r,e=[],t){let s={cwd:kl.tmpdir(),env:{COREPACK_ENABLE_STRICT:"0"},...t};try{let{stdout:i}=await _l(r,e,s);return i.trim()}catch{return}}async function Rl(){let[r,e,t,s]=await Promise.all([Nr("yarnpkg",["--version"],{cwd:"."}),Nr("npm",["--version"]),Nr("pnpm",["--version"]),Nr("bun",["--version"])]);return{yarn:r,pnpm:t,npm:e,bun:s}}u();u();u();var Ml=Tr(Eo());u();u();function ii(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}u();u();function ar(r){return ar=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ar(r)}u();function ni(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function oi(r,e){if(e&&(ar(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ni(r)}u();function lt(r){return lt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},lt(r)}u();u();function Se(r,e){return Se=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(s,i){return s.__proto__=i,s},Se(r,e)}function ui(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&Se(r,e)}u();u();function ai(r){return Function.toString.call(r).indexOf("[native code]")!==-1}u();u();function ci(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Rt(r,e,t){return ci()?Rt=Reflect.construct.bind():Rt=function(i,n,o){var a=[null];a.push.apply(a,n);var l=Function.bind.apply(i,a),c=new l;return o&&Se(c,o.prototype),c},Rt.apply(null,arguments)}function cr(r){var e=typeof Map=="function"?new Map:void 0;return cr=function(s){if(s===null||!ai(s))return s;if(typeof s!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e<"u"){if(e.has(s))return e.get(s);e.set(s,i)}function i(){return Rt(s,arguments,lt(this).constructor)}return i.prototype=Object.create(s.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),Se(i,s)},cr(r)}var Lo=Tr(No());import fd from"fs-extra";var pd=function(r){ui(e,r);function e(t){var s;return ii(this,e),s=oi(this,lt(e).call(this,"No package.json could be found upwards from the directory ".concat(t))),s.directory=t,s}return e}(cr(Error));u();u();u();import Fd from"js-yaml";import{sync as Sd}from"fast-glob";u();import vd from"fs-extra";u();import Td from"fs-extra";import Nd from"picocolors";u();import{Stream as Fp}from"stream";import{promisify as bp}from"util";var Sp=Tr(Pc());var N0=bp(Fp.pipeline);u();import I0 from"fs-extra";u();var Np=Tr(Uc());import G0 from"picocolors";import J0 from"fs-extra";u();u();u();var B=class extends Error{constructor(t,s){var i;super(t);this.name="ConvertError",this.type=(i=s==null?void 0:s.type)!=null?i:"unknown",Error.captureStackTrace(this,B)}};u();u();import er from"path";import we from"fs-extra";import Jp from"execa";u();import zp from"path";import Wp from"fs-extra";import Gp from"picocolors";u();import rt from"path";import Lp from"execa";import{readJsonSync as Mp,existsSync as ro,readFileSync as Ip,rmSync as $c,writeFile as jp}from"fs-extra";import{sync as qp}from"fast-glob";import Up from"js-yaml";var $p=/^(?!_)(?<manager>.+)@(?<version>.+)$/;function I({workspaceRoot:r}){let e=rt.join(r,"package.json");try{return Mp(e,"utf8")}catch(t){if(t&&typeof t=="object"&&"code"in t){if(t.code==="ENOENT")throw new B(`no "package.json" found at ${r}`,{type:"package_json-missing"});if(t.code==="EJSONPARSE")throw new B(`failed to parse "package.json" at ${r}`,{type:"package_json-parse_error"})}throw new Error(`unexpected error reading "package.json" at ${r}`)}}function st({workspaceRoot:r}){let{packageManager:e}=I({workspaceRoot:r});if(e)try{let t=$p.exec(e);if(t){let[s,i]=t;return i}}catch{}}function je({workspaceRoot:r}){let e=I({workspaceRoot:r}),t=rt.basename(r),{name:s=t,description:i}=e;return{name:s,description:i}}function so({workspaceRoot:r}){let e=rt.join(r,"pnpm-workspace.yaml");if(ro(e))try{let t=Up.load(Ip(e,"utf8"));if(t instanceof Object&&"packages"in t&&Array.isArray(t.packages))return t.packages}catch{throw new B(`failed to parse ${e}`,{type:"pnpm-workspace_parse_error"})}return[]}function it({root:r,lockFile:e,workspaceConfig:t}){let s=n=>rt.join(r,n),i={root:r,lockfile:s(e),packageJson:s("package.json"),nodeModules:s("node_modules")};return t&&(i.workspaceConfig=s(t)),i}function Qt({workspaces:r}){var e;return r?Array.isArray(r)?r:"packages"in r?(e=r.packages)!=null?e:[]:[]:[]}function nt({workspaceRoot:r,workspaceGlobs:e}){if(!e)return[];let t=e.filter(s=>s.startsWith("!")).map(s=>s.slice(1));return e.filter(s=>!s.startsWith("!")).flatMap(s=>{let i=[`${s}/package.json`];return qp(i,{onlyFiles:!0,absolute:!0,cwd:r,ignore:["**/node_modules/**",...t]})}).map(s=>{let i=rt.dirname(s),{name:n,description:o}=je({workspaceRoot:i});return{name:n,description:o,paths:{root:i,packageJson:s,nodeModules:rt.join(i,"node_modules")}}})}function zc({directory:r}){let e=rt.resolve(process.cwd(),r);return{exists:ro(e),absolute:e}}function ce({packageManager:r,action:e,project:t}){let s=t.workspaceData.globs.length>0;return`${e==="remove"?"Removing":"Adding"} ${r} ${s?"workspaces":""} ${e==="remove"?"from":"to"} ${t.name}`}function Wc({project:r}){let e=t=>!(t.includes("*")&&(t.includes("**")||t.split("/").slice(0,-1).join("/").includes("*"))||["!","[","]","{","}"].some(s=>t.includes(s)));return r.workspaceData.globs.every(e)}function re({project:r,options:e}){e!=null&&e.dry||$c(r.paths.lockfile,{force:!0})}async function Gs({project:r,options:e}){if(!(e!=null&&e.dry)&&ro(r.paths.lockfile))try{let{stdout:t}=await Lp("bun",["bun.lockb"],{stdin:"ignore",cwd:r.paths.root});await jp(rt.join(r.paths.root,"yarn.lock"),t)}catch{}finally{$c(r.paths.lockfile,{force:!0})}}function Hp({dependencyList:r,project:e,to:t}){let s=[];return e.workspaceData.workspaces.forEach(i=>{let{name:n}=i;if(r[n]){let o=r[n],a=o.startsWith("workspace:")?o.slice(10):o;r[n]=t.name==="pnpm"?`workspace:${a}`:a,s.push(n)}}),{dependencyList:r,updated:s}}function le({project:r,workspace:e,to:t,logger:s,options:i}){if(["yarn","npm","bun"].includes(t.name)&&["yarn","npm","bun"].includes(r.packageManager))return;let n=I({workspaceRoot:e.paths.root}),o={dependencies:[],devDependencies:[],peerDependencies:[],optionalDependencies:[]},a=["dependencies","devDependencies","peerDependencies","optionalDependencies"];a.forEach(d=>{let g=n[d];if(g){let{updated:w,dependencyList:S}=Hp({dependencyList:g,project:r,to:t});n[d]=S,o[d]=w}});let l=d=>{let g=o[d].length;if(g>0)return`${Gp.green(g.toString())} ${d}`},c=a.map(l).filter(Boolean),h=`./${zp.relative(r.paths.root,e.paths.packageJson)}`;if(c.length>=1){let d="updating";c.forEach((g,w)=>{c.length===1?d+=` ${g} in ${h}`:w===c.length-1?d+=`and ${g} in ${h}`:d+=` ${g}, `}),s.workspaceStep(d)}else s.workspaceStep(`no workspace dependencies found in ${h}`);i!=null&&i.dry||Wp.writeJSONSync(e.paths.packageJson,n,{spaces:2})}var ot={name:"pnpm",lock:"pnpm-lock.yaml"};async function Gc(r){let e=er.join(r.workspaceRoot,ot.lock),t=er.join(r.workspaceRoot,"pnpm-workspace.yaml"),s=st({workspaceRoot:r.workspaceRoot});return we.existsSync(e)||we.existsSync(t)||s===ot.name}async function Vp(r){if(!await Gc(r))throw new B("Not a pnpm project",{type:"package_manager-unexpected"});let{name:t,description:s}=je(r);return{name:t,description:s,packageManager:ot.name,paths:it({root:r.workspaceRoot,lockFile:ot.lock,workspaceConfig:"pnpm-workspace.yaml"}),workspaceData:{globs:so(r),workspaces:nt({workspaceGlobs:so(r),...r})}}}async function Yp(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;s.mainStep(ce({action:"create",packageManager:ot.name,project:e}));let o=I({workspaceRoot:e.paths.root});s.rootHeader(),o.packageManager=`${t.name}@${t.version}`,s.rootStep(`adding "packageManager" field to ${e.name} root "package.json"`),i!=null&&i.dry||(we.writeJSONSync(e.paths.packageJson,o,{spaces:2}),n&&(s.rootStep('adding "pnpm-workspace.yaml"'),we.writeFileSync(er.join(e.paths.root,"pnpm-workspace.yaml"),`packages:
${e.workspaceData.globs.map(a=>`  - "${a}"`).join(`
`)}`))),n&&(le({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{le({workspace:a,project:e,to:t,logger:s,options:i})}))}async function Kp(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(ce({action:"remove",packageManager:ot.name,project:e}));let n=I({workspaceRoot:e.paths.root});if(e.paths.workspaceConfig&&i&&(t.subStep('removing "pnpm-workspace.yaml"'),s!=null&&s.dry||we.rmSync(e.paths.workspaceConfig,{force:!0})),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){we.writeJSONSync(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>we.rm(a,{recursive:!0,force:!0})))}catch{throw new B("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function Zp(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${er.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||we.rmSync(e.paths.lockfile,{force:!0})}async function Xp(r){let{project:e,options:t,logger:s}=r,i=()=>{s.subStep(`converting ${er.relative(e.paths.root,e.paths.lockfile)} to ${ot.lock}`)},n=async()=>{if(!(t!=null&&t.dry)&&we.existsSync(e.paths.lockfile))try{await Jp(ot.name,["import"],{stdio:"ignore",cwd:e.paths.root})}catch{}finally{re({project:e,options:t})}};switch(e.packageManager){case"pnpm":break;case"bun":i(),await Gs({project:e,options:t}),await n(),we.rmSync(er.join(e.paths.root,"yarn.lock"),{force:!0});break;case"npm":i(),await n();break;case"yarn":i(),await n();break}}var Hc={detect:Gc,read:Vp,create:Yp,remove:Kp,clean:Zp,convertLock:Xp};u();import Hs from"path";import tr from"fs-extra";var rr={name:"npm",lock:"package-lock.json"};async function Jc(r){let e=Hs.join(r.workspaceRoot,rr.lock),t=st({workspaceRoot:r.workspaceRoot});return tr.existsSync(e)||t===rr.name}async function Qp(r){if(!await Jc(r))throw new B("Not an npm project",{type:"package_manager-unexpected"});let t=I(r),{name:s,description:i}=je(r),n=Qt({workspaces:t.workspaces});return{name:s,description:i,packageManager:rr.name,paths:it({root:r.workspaceRoot,lockFile:rr.lock}),workspaceData:{globs:n,workspaces:nt({workspaceGlobs:n,...r})}}}async function em(r){let{project:e,options:t,to:s,logger:i}=r,n=e.workspaceData.globs.length>0;i.mainStep(ce({packageManager:rr.name,action:"create",project:e}));let o=I({workspaceRoot:e.paths.root});i.rootHeader(),i.rootStep(`adding "packageManager" field to ${Hs.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${s.name}@${s.version}`,n?(i.rootStep(`adding "workspaces" field to ${Hs.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,t!=null&&t.dry||tr.writeJSONSync(e.paths.packageJson,o,{spaces:2}),le({workspace:{name:"root",paths:e.paths},project:e,to:s,logger:i,options:t}),i.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{le({workspace:a,project:e,to:s,logger:i,options:t})})):t!=null&&t.dry||tr.writeJSONSync(e.paths.packageJson,o,{spaces:2})}async function tm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(ce({packageManager:rr.name,action:"remove",project:e}));let n=I({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){tr.writeJSONSync(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>tr.rm(a,{recursive:!0,force:!0})))}catch{throw new B("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function rm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${Hs.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||tr.rmSync(e.paths.lockfile,{force:!0})}async function sm(r){let{project:e,options:t}=r;switch(e.packageManager){case"pnpm":re({project:e,options:t});break;case"bun":re({project:e,options:t});break;case"npm":break;case"yarn":re({project:e,options:t});break}}var Vc={detect:Jc,read:Qp,create:em,remove:tm,clean:rm,convertLock:sm};u();import vr from"path";import sr from"fs-extra";var bt={name:"yarn",lock:"yarn.lock"};async function Yc(r){let e=vr.join(r.workspaceRoot,bt.lock),t=st({workspaceRoot:r.workspaceRoot});return sr.existsSync(e)||t===bt.name}async function im(r){if(!await Yc(r))throw new B("Not a yarn project",{type:"package_manager-unexpected"});let t=I(r),{name:s,description:i}=je(r),n=Qt({workspaces:t.workspaces});return{name:s,description:i,packageManager:bt.name,paths:it({root:r.workspaceRoot,lockFile:bt.lock}),workspaceData:{globs:n,workspaces:nt({workspaceGlobs:n,...r})}}}async function nm(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;s.mainStep(ce({packageManager:bt.name,action:"create",project:e}));let o=I({workspaceRoot:e.paths.root});s.rootHeader(),s.rootStep(`adding "packageManager" field to ${vr.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${t.name}@${t.version}`,n?(s.rootStep(`adding "workspaces" field to ${vr.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,i!=null&&i.dry||sr.writeJSONSync(e.paths.packageJson,o,{spaces:2}),le({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{le({workspace:a,project:e,to:t,logger:s,options:i})})):i!=null&&i.dry||sr.writeJSONSync(e.paths.packageJson,o,{spaces:2})}async function om(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(ce({packageManager:bt.name,action:"remove",project:e}));let n=I({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){sr.writeJSONSync(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>sr.rm(a,{recursive:!0,force:!0})))}catch{throw new B("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function um(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${vr.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||sr.rmSync(e.paths.lockfile,{force:!0})}async function am(r){let{project:e,options:t,logger:s}=r,i=()=>{s.subStep(`converting ${vr.relative(e.paths.root,e.paths.lockfile)} to ${bt.lock}`)};switch(e.packageManager){case"pnpm":re({project:e,options:t});break;case"bun":i(),await Gs({project:e,options:t});break;case"npm":re({project:e,options:t});break;case"yarn":break}}var Kc={detect:Yc,read:im,create:nm,remove:om,clean:um,convertLock:am};u();import Js from"path";import ir from"fs-extra";var nr={name:"bun",lock:"bun.lockb"};async function Zc(r){let e=Js.join(r.workspaceRoot,nr.lock),t=st({workspaceRoot:r.workspaceRoot});return ir.existsSync(e)||t===nr.name}async function cm(r){if(!await Zc(r))throw new B("Not a bun project",{type:"package_manager-unexpected"});let t=I(r),{name:s,description:i}=je(r),n=Qt({workspaces:t.workspaces});return{name:s,description:i,packageManager:nr.name,paths:it({root:r.workspaceRoot,lockFile:nr.lock}),workspaceData:{globs:n,workspaces:nt({workspaceGlobs:n,...r})}}}async function lm(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;if(!Wc({project:e}))throw new B("Unable to convert project to bun - workspace globs unsupported",{type:"bun-workspace_glob_error"});s.mainStep(ce({packageManager:nr.name,action:"create",project:e}));let o=I({workspaceRoot:e.paths.root});s.rootHeader(),s.rootStep(`adding "packageManager" field to ${Js.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${t.name}@${t.version}`,n?(s.rootStep(`adding "workspaces" field to ${Js.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,i!=null&&i.dry||ir.writeJSONSync(e.paths.packageJson,o,{spaces:2}),le({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{le({workspace:a,project:e,to:t,logger:s,options:i})})):i!=null&&i.dry||ir.writeJSONSync(e.paths.packageJson,o,{spaces:2})}async function hm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(ce({packageManager:nr.name,action:"remove",project:e}));let n=I({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){ir.writeJSONSync(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>ir.rm(a,{recursive:!0,force:!0})))}catch{throw new B("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function fm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${Js.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||ir.rmSync(e.paths.lockfile,{force:!0})}async function pm(r){let{project:e,options:t}=r;switch(e.packageManager){case"pnpm":re({project:e,options:t});break;case"bun":break;case"npm":re({project:e,options:t});break;case"yarn":re({project:e,options:t});break}}var Xc={detect:Zc,read:cm,create:lm,remove:hm,clean:fm,convertLock:pm};var St={pnpm:Hc,yarn:Kc,npm:Vc,bun:Xc};u();async function fg({root:r}){let{exists:e,absolute:t}=zc({directory:r});if(!e)throw new B(`Could not find directory at ${t}. Ensure the directory exists.`,{type:"invalid_directory"});for(let{detect:s,read:i}of Object.values(St))if(await s({workspaceRoot:t}))return i({workspaceRoot:t});throw new B("Could not determine package manager. Add `packageManager` to `package.json` or ensure a lockfile is present.",{type:"package_manager-unable_to_detect"})}u();import he from"picocolors";import mm from"gradient-string";var or=2,Vs=class{constructor({interactive:e,dry:t}={}){this.interactive=e!=null?e:!0,this.dry=t!=null?t:!1,this.step=1}logger(...e){this.interactive&&console.log(...e)}indented(e,...t){this.logger(" ".repeat(or*e),...t)}header(e){this.blankLine(),this.logger(he.bold(e))}installerFrames(){let e=`${" ".repeat(or)} - ${this.dry?he.yellow("SKIPPED | "):he.green("OK | ")}`;return[`${e}   `,`${e}>  `,`${e}>> `,`${e}>>>`]}gradient(e){return mm("#0099F7","#F11712")(e.toString())}hero(){this.logger(he.bold(this.gradient(`
>>> TURBOREPO
`)))}info(...e){this.logger(...e)}mainStep(e){this.blankLine(),this.logger(`${this.step}. ${he.underline(e)}`),this.step+=1}subStep(...e){this.logger(" ".repeat(or),"-",this.dry?he.yellow("SKIPPED |"):he.green("OK |"),...e)}subStepFailure(...e){this.logger(" ".repeat(or),"-",he.red("ERROR |"),...e)}rootHeader(){this.blankLine(),this.indented(2,"Root:")}rootStep(...e){this.logger(" ".repeat(or*3),"-",this.dry?he.yellow("SKIPPED |"):he.green("OK |"),...e)}workspaceHeader(){this.blankLine(),this.indented(2,"Workspaces:")}workspaceStep(...e){this.logger(" ".repeat(or*3),"-",this.dry?he.yellow("SKIPPED |"):he.green("OK |"),...e)}blankLine(){this.logger()}error(...e){console.error(...e)}};u();import dm from"execa";import Dm from"ora";import{satisfies as gm}from"semver";var Qc={npm:[{name:"npm",template:"npm",command:"npm",installArgs:["install"],version:"latest",executable:"npx",semver:"*",default:!0}],pnpm:[{name:"pnpm6",template:"pnpm",command:"pnpm",installArgs:["install"],version:"latest-6",executable:"pnpx",semver:"6.x"},{name:"pnpm",template:"pnpm",command:"pnpm",installArgs:["install","--fix-lockfile"],version:"latest",executable:"pnpm dlx",semver:">=7",default:!0}],yarn:[{name:"yarn",template:"yarn",command:"yarn",installArgs:["install"],version:"1.x",executable:"npx",semver:"<2",default:!0},{name:"berry",template:"berry",command:"yarn",installArgs:["install","--no-immutable"],version:"stable",executable:"yarn dlx",semver:">=2"}],bun:[{name:"bun",template:"bun",command:"bun",installArgs:["install"],version:"latest",executable:"bunx",semver:"^1.0.1",default:!0}]};function Em(r){let{version:e,name:t}=r;return e?Qc[t].find(s=>gm(e,s.semver)):Qc[t].find(s=>s.default)}async function el(r){let{to:e,logger:t,options:s}=r,i=t!=null?t:new Vs(s),n=Em(e);if(!n)throw new B("Unsupported package manager version.",{type:"package_manager-unsupported_version"});if(i.subStep(`running "${n.command} ${n.installArgs.join(" ")}"`),!(s!=null&&s.dry)){let o;i.interactive&&(o=Dm({text:"installing dependencies...",spinner:{frames:i.installerFrames()}}).start());try{await dm(n.command,n.installArgs,{cwd:r.project.paths.root}),o&&o.stop(),i.subStep("dependencies installed")}catch(a){throw i.subStepFailure("failed to install dependencies"),a}}}u();import ym from"picocolors";async function _g({project:r,convertTo:e,logger:t,options:s}){if(t.header(`Converting project from ${r.packageManager} to ${e.name}.`),!(s!=null&&s.ignoreUnchangedPackageManager)){if(r.packageManager===e.name)throw new B("You are already using this package manager",{type:"package_manager-already_in_use"});if(!e.version)throw new B(`${e.name} is not installed, or could not be located`,{type:"package_manager-could_not_be_found"})}let i=e;s!=null&&s.ignoreUnchangedPackageManager||await St[r.packageManager].remove({project:r,to:i,logger:t,options:s}),await St[i.name].create({project:r,to:i,logger:t,options:s}),t.mainStep("Installing dependencies"),s!=null&&s.skipInstall?t.subStep(ym.yellow("Skipping install")):(await St[i.name].convertLock({project:r,to:i,logger:t,options:s}),await el({project:r,to:i,logger:t,options:s})),t.mainStep(`Cleaning up ${r.packageManager} workspaces`),r.packageManager!==e.name&&await St[r.packageManager].clean({project:r,logger:t})}export{u as a,Pr as b,Rl as c,B as d,zc as e,St as f,fg as g,Vs as h,Em as i,el as j,_g as k};
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
