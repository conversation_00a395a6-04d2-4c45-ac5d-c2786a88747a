"use strict";var Dl=Object.create;var Pr=Object.defineProperty;var gl=Object.getOwnPropertyDescriptor;var El=Object.getOwnPropertyNames;var yl=Object.getPrototypeOf,Al=Object.prototype.hasOwnProperty;var Cl=(r,e)=>()=>(r&&(e=r(r=0)),e);var E=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),wl=(r,e)=>{for(var t in e)Pr(r,t,{get:e[t],enumerable:!0})},no=(r,e,t,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of El(e))!Al.call(r,i)&&i!==t&&Pr(r,i,{get:()=>e[i],enumerable:!(s=gl(e,i))||s.enumerable});return r};var F=(r,e,t)=>(t=r!=null?Dl(yl(r)):{},no(e||!r||!r.__esModule?Pr(t,"default",{value:r,enumerable:!0}):t,r)),Fl=r=>no(Pr({},"__esModule",{value:!0}),r);var u=Cl(()=>{});var oo=E((wm,Fe)=>{u();function Xs(r){return Fe.exports=Xs=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Fe.exports.__esModule=!0,Fe.exports.default=Fe.exports,Xs(r)}Fe.exports=Xs,Fe.exports.__esModule=!0,Fe.exports.default=Fe.exports});var co=E((Fm,be)=>{u();var uo=oo().default;function ao(){"use strict";be.exports=ao=function(){return e},be.exports.__esModule=!0,be.exports.default=be.exports;var r,e={},t=Object.prototype,s=t.hasOwnProperty,i=Object.defineProperty||function(g,p,d){g[p]=d.value},n=typeof Symbol=="function"?Symbol:{},o=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function c(g,p,d){return Object.defineProperty(g,p,{value:d,enumerable:!0,configurable:!0,writable:!0}),g[p]}try{c({},"")}catch{c=function(d,y,w){return d[y]=w}}function h(g,p,d,y){var w=p&&p.prototype instanceof x?p:x,C=Object.create(w.prototype),P=new Ks(y||[]);return i(C,"_invoke",{value:ml(g,d,P)}),C}function m(g,p,d){try{return{type:"normal",arg:g.call(p,d)}}catch(y){return{type:"throw",arg:y}}}e.wrap=h;var D="suspendedStart",A="suspendedYield",b="executing",T="completed",S={};function x(){}function ut(){}function de(){}var Js={};c(Js,o,function(){return this});var Vs=Object.getPrototypeOf,Or=Vs&&Vs(Vs(Zs([])));Or&&Or!==t&&s.call(Or,o)&&(Js=Or);var Bt=de.prototype=x.prototype=Object.create(Js);function so(g){["next","throw","return"].forEach(function(p){c(g,p,function(d){return this._invoke(p,d)})})}function Tr(g,p){function d(w,C,P,G){var H=m(g[w],g,C);if(H.type!=="throw"){var at=H.arg,$e=at.value;return $e&&uo($e)=="object"&&s.call($e,"__await")?p.resolve($e.__await).then(function(ct){d("next",ct,P,G)},function(ct){d("throw",ct,P,G)}):p.resolve($e).then(function(ct){at.value=ct,P(at)},function(ct){return d("throw",ct,P,G)})}G(H.arg)}var y;i(this,"_invoke",{value:function(C,P){function G(){return new p(function(H,at){d(C,P,H,at)})}return y=y?y.then(G,G):G()}})}function ml(g,p,d){var y=D;return function(w,C){if(y===b)throw new Error("Generator is already running");if(y===T){if(w==="throw")throw C;return{value:r,done:!0}}for(d.method=w,d.arg=C;;){var P=d.delegate;if(P){var G=io(P,d);if(G){if(G===S)continue;return G}}if(d.method==="next")d.sent=d._sent=d.arg;else if(d.method==="throw"){if(y===D)throw y=T,d.arg;d.dispatchException(d.arg)}else d.method==="return"&&d.abrupt("return",d.arg);y=b;var H=m(g,p,d);if(H.type==="normal"){if(y=d.done?T:A,H.arg===S)continue;return{value:H.arg,done:d.done}}H.type==="throw"&&(y=T,d.method="throw",d.arg=H.arg)}}}function io(g,p){var d=p.method,y=g.iterator[d];if(y===r)return p.delegate=null,d==="throw"&&g.iterator.return&&(p.method="return",p.arg=r,io(g,p),p.method==="throw")||d!=="return"&&(p.method="throw",p.arg=new TypeError("The iterator does not provide a '"+d+"' method")),S;var w=m(y,g.iterator,p.arg);if(w.type==="throw")return p.method="throw",p.arg=w.arg,p.delegate=null,S;var C=w.arg;return C?C.done?(p[g.resultName]=C.value,p.next=g.nextLoc,p.method!=="return"&&(p.method="next",p.arg=r),p.delegate=null,S):C:(p.method="throw",p.arg=new TypeError("iterator result is not an object"),p.delegate=null,S)}function dl(g){var p={tryLoc:g[0]};1 in g&&(p.catchLoc=g[1]),2 in g&&(p.finallyLoc=g[2],p.afterLoc=g[3]),this.tryEntries.push(p)}function Ys(g){var p=g.completion||{};p.type="normal",delete p.arg,g.completion=p}function Ks(g){this.tryEntries=[{tryLoc:"root"}],g.forEach(dl,this),this.reset(!0)}function Zs(g){if(g||g===""){var p=g[o];if(p)return p.call(g);if(typeof g.next=="function")return g;if(!isNaN(g.length)){var d=-1,y=function w(){for(;++d<g.length;)if(s.call(g,d))return w.value=g[d],w.done=!1,w;return w.value=r,w.done=!0,w};return y.next=y}}throw new TypeError(uo(g)+" is not iterable")}return ut.prototype=de,i(Bt,"constructor",{value:de,configurable:!0}),i(de,"constructor",{value:ut,configurable:!0}),ut.displayName=c(de,l,"GeneratorFunction"),e.isGeneratorFunction=function(g){var p=typeof g=="function"&&g.constructor;return!!p&&(p===ut||(p.displayName||p.name)==="GeneratorFunction")},e.mark=function(g){return Object.setPrototypeOf?Object.setPrototypeOf(g,de):(g.__proto__=de,c(g,l,"GeneratorFunction")),g.prototype=Object.create(Bt),g},e.awrap=function(g){return{__await:g}},so(Tr.prototype),c(Tr.prototype,a,function(){return this}),e.AsyncIterator=Tr,e.async=function(g,p,d,y,w){w===void 0&&(w=Promise);var C=new Tr(h(g,p,d,y),w);return e.isGeneratorFunction(p)?C:C.next().then(function(P){return P.done?P.value:C.next()})},so(Bt),c(Bt,l,"Generator"),c(Bt,o,function(){return this}),c(Bt,"toString",function(){return"[object Generator]"}),e.keys=function(g){var p=Object(g),d=[];for(var y in p)d.push(y);return d.reverse(),function w(){for(;d.length;){var C=d.pop();if(C in p)return w.value=C,w.done=!1,w}return w.done=!0,w}},e.values=Zs,Ks.prototype={constructor:Ks,reset:function(p){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(Ys),!p)for(var d in this)d.charAt(0)==="t"&&s.call(this,d)&&!isNaN(+d.slice(1))&&(this[d]=r)},stop:function(){this.done=!0;var p=this.tryEntries[0].completion;if(p.type==="throw")throw p.arg;return this.rval},dispatchException:function(p){if(this.done)throw p;var d=this;function y(at,$e){return P.type="throw",P.arg=p,d.next=at,$e&&(d.method="next",d.arg=r),!!$e}for(var w=this.tryEntries.length-1;w>=0;--w){var C=this.tryEntries[w],P=C.completion;if(C.tryLoc==="root")return y("end");if(C.tryLoc<=this.prev){var G=s.call(C,"catchLoc"),H=s.call(C,"finallyLoc");if(G&&H){if(this.prev<C.catchLoc)return y(C.catchLoc,!0);if(this.prev<C.finallyLoc)return y(C.finallyLoc)}else if(G){if(this.prev<C.catchLoc)return y(C.catchLoc,!0)}else{if(!H)throw new Error("try statement without catch or finally");if(this.prev<C.finallyLoc)return y(C.finallyLoc)}}}},abrupt:function(p,d){for(var y=this.tryEntries.length-1;y>=0;--y){var w=this.tryEntries[y];if(w.tryLoc<=this.prev&&s.call(w,"finallyLoc")&&this.prev<w.finallyLoc){var C=w;break}}C&&(p==="break"||p==="continue")&&C.tryLoc<=d&&d<=C.finallyLoc&&(C=null);var P=C?C.completion:{};return P.type=p,P.arg=d,C?(this.method="next",this.next=C.finallyLoc,S):this.complete(P)},complete:function(p,d){if(p.type==="throw")throw p.arg;return p.type==="break"||p.type==="continue"?this.next=p.arg:p.type==="return"?(this.rval=this.arg=p.arg,this.method="return",this.next="end"):p.type==="normal"&&d&&(this.next=d),S},finish:function(p){for(var d=this.tryEntries.length-1;d>=0;--d){var y=this.tryEntries[d];if(y.finallyLoc===p)return this.complete(y.completion,y.afterLoc),Ys(y),S}},catch:function(p){for(var d=this.tryEntries.length-1;d>=0;--d){var y=this.tryEntries[d];if(y.tryLoc===p){var w=y.completion;if(w.type==="throw"){var C=w.arg;Ys(y)}return C}}throw new Error("illegal catch attempt")},delegateYield:function(p,d,y){return this.delegate={iterator:Zs(p),resultName:d,nextLoc:y},this.method==="next"&&(this.arg=r),S}},e}be.exports=ao,be.exports.__esModule=!0,be.exports.default=be.exports});var ho=E((bm,lo)=>{u();var Nr=co()();lo.exports=Nr;try{regeneratorRuntime=Nr}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=Nr:Function("r","regeneratorRuntime = r")(Nr)}});var po=E((Hm,ni)=>{"use strict";u();var fo=(r,...e)=>new Promise(t=>{t(r(...e))});ni.exports=fo;ni.exports.default=fo});var Do=E((Jm,oi)=>{"use strict";u();var bl=po(),mo=r=>{if(!((Number.isInteger(r)||r===1/0)&&r>0))return Promise.reject(new TypeError("Expected `concurrency` to be a number from 1 and up"));let e=[],t=0,s=()=>{t--,e.length>0&&e.shift()()},i=(a,l,...c)=>{t++;let h=bl(a,...c);l(h),h.then(s,s)},n=(a,l,...c)=>{t<r?i(a,l,...c):e.push(i.bind(null,a,l,...c))},o=(a,...l)=>new Promise(c=>n(a,c,...l));return Object.defineProperties(o,{activeCount:{get:()=>t},pendingCount:{get:()=>e.length},clearQueue:{value:()=>{e.length=0}}}),o};oi.exports=mo;oi.exports.default=mo});var yo=E((Vm,ui)=>{"use strict";u();var go=Do(),Lr=class extends Error{constructor(e){super(),this.value=e}},Sl=async(r,e)=>e(await r),kl=async r=>{let e=await Promise.all(r);if(e[1]===!0)throw new Lr(e[0]);return!1},Eo=async(r,e,t)=>{t={concurrency:1/0,preserveOrder:!0,...t};let s=go(t.concurrency),i=[...r].map(o=>[o,s(Sl,o,e)]),n=go(t.preserveOrder?1:1/0);try{await Promise.all(i.map(o=>n(kl,o)))}catch(o){if(o instanceof Lr)return o.value;throw o}};ui.exports=Eo;ui.exports.default=Eo});var So=E((Ym,ai)=>{"use strict";u();var Ao=require("path"),Mr=require("fs"),{promisify:Co}=require("util"),_l=yo(),Rl=Co(Mr.stat),Bl=Co(Mr.lstat),wo={directory:"isDirectory",file:"isFile"};function Fo({type:r}){if(!(r in wo))throw new Error(`Invalid type specified: ${r}`)}var bo=(r,e)=>r===void 0||e[wo[r]]();ai.exports=async(r,e)=>{e={cwd:process.cwd(),type:"file",allowSymlinks:!0,...e},Fo(e);let t=e.allowSymlinks?Rl:Bl;return _l(r,async s=>{try{let i=await t(Ao.resolve(e.cwd,s));return bo(e.type,i)}catch{return!1}},e)};ai.exports.sync=(r,e)=>{e={cwd:process.cwd(),allowSymlinks:!0,type:"file",...e},Fo(e);let t=e.allowSymlinks?Mr.statSync:Mr.lstatSync;for(let s of r)try{let i=t(Ao.resolve(e.cwd,s));if(bo(e.type,i))return s}catch{}}});var _o=E((Km,ci)=>{"use strict";u();var ko=require("fs"),{promisify:vl}=require("util"),xl=vl(ko.access);ci.exports=async r=>{try{return await xl(r),!0}catch{return!1}};ci.exports.sync=r=>{try{return ko.accessSync(r),!0}catch{return!1}}});var Bo=E((Zm,xt)=>{"use strict";u();var We=require("path"),Ir=So(),Ro=_o(),li=Symbol("findUp.stop");xt.exports=async(r,e={})=>{let t=We.resolve(e.cwd||""),{root:s}=We.parse(t),i=[].concat(r),n=async o=>{if(typeof r!="function")return Ir(i,o);let a=await r(o.cwd);return typeof a=="string"?Ir([a],o):a};for(;;){let o=await n({...e,cwd:t});if(o===li)return;if(o)return We.resolve(t,o);if(t===s)return;t=We.dirname(t)}};xt.exports.sync=(r,e={})=>{let t=We.resolve(e.cwd||""),{root:s}=We.parse(t),i=[].concat(r),n=o=>{if(typeof r!="function")return Ir.sync(i,o);let a=r(o.cwd);return typeof a=="string"?Ir.sync([a],o):a};for(;;){let o=n({...e,cwd:t});if(o===li)return;if(o)return We.resolve(t,o);if(t===s)return;t=We.dirname(t)}};xt.exports.exists=Ro;xt.exports.sync.exists=Ro.sync;xt.exports.stop=li});var Tt=E((kd,Io)=>{"use strict";u();var Mo=new Map([["C","cwd"],["f","file"],["z","gzip"],["P","preservePaths"],["U","unlink"],["strip-components","strip"],["stripComponents","strip"],["keep-newer","newer"],["keepNewer","newer"],["keep-newer-files","newer"],["keepNewerFiles","newer"],["k","keep"],["keep-existing","keep"],["keepExisting","keep"],["m","noMtime"],["no-mtime","noMtime"],["p","preserveOwner"],["L","follow"],["h","follow"]]);Io.exports=r=>r?Object.keys(r).map(e=>[Mo.has(e)?Mo.get(e):e,r[e]]).reduce((e,t)=>(e[t[0]]=t[1],e),Object.create(null)):{}});var Gr=E((_d,Jo)=>{"use strict";u();var jo=typeof process=="object"&&process?process:{stdout:null,stderr:null},Zl=require("events"),qo=require("stream"),Uo=require("string_decoder").StringDecoder,ke=Symbol("EOF"),_e=Symbol("maybeEmitEnd"),ze=Symbol("emittedEnd"),qr=Symbol("emittingEnd"),lr=Symbol("emittedError"),Ur=Symbol("closed"),$o=Symbol("read"),$r=Symbol("flush"),Wo=Symbol("flushChunk"),J=Symbol("encoding"),Re=Symbol("decoder"),Wr=Symbol("flowing"),hr=Symbol("paused"),Pt=Symbol("resume"),N=Symbol("buffer"),De=Symbol("pipes"),I=Symbol("bufferLength"),mi=Symbol("bufferPush"),di=Symbol("bufferShift"),q=Symbol("objectMode"),U=Symbol("destroyed"),Di=Symbol("emitData"),zo=Symbol("emitEnd"),gi=Symbol("emitEnd2"),Be=Symbol("async"),fr=r=>Promise.resolve().then(r),Go=global._MP_NO_ITERATOR_SYMBOLS_!=="1",Xl=Go&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),Ql=Go&&Symbol.iterator||Symbol("iterator not implemented"),eh=r=>r==="end"||r==="finish"||r==="prefinish",th=r=>r instanceof ArrayBuffer||typeof r=="object"&&r.constructor&&r.constructor.name==="ArrayBuffer"&&r.byteLength>=0,rh=r=>!Buffer.isBuffer(r)&&ArrayBuffer.isView(r),zr=class{constructor(e,t,s){this.src=e,this.dest=t,this.opts=s,this.ondrain=()=>e[Pt](),t.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}},Ei=class extends zr{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(e,t,s){super(e,t,s),this.proxyErrors=i=>t.emit("error",i),e.on("error",this.proxyErrors)}};Jo.exports=class Ho extends qo{constructor(e){super(),this[Wr]=!1,this[hr]=!1,this[De]=[],this[N]=[],this[q]=e&&e.objectMode||!1,this[q]?this[J]=null:this[J]=e&&e.encoding||null,this[J]==="buffer"&&(this[J]=null),this[Be]=e&&!!e.async||!1,this[Re]=this[J]?new Uo(this[J]):null,this[ke]=!1,this[ze]=!1,this[qr]=!1,this[Ur]=!1,this[lr]=null,this.writable=!0,this.readable=!0,this[I]=0,this[U]=!1,e&&e.debugExposeBuffer===!0&&Object.defineProperty(this,"buffer",{get:()=>this[N]}),e&&e.debugExposePipes===!0&&Object.defineProperty(this,"pipes",{get:()=>this[De]})}get bufferLength(){return this[I]}get encoding(){return this[J]}set encoding(e){if(this[q])throw new Error("cannot set encoding in objectMode");if(this[J]&&e!==this[J]&&(this[Re]&&this[Re].lastNeed||this[I]))throw new Error("cannot change encoding");this[J]!==e&&(this[Re]=e?new Uo(e):null,this[N].length&&(this[N]=this[N].map(t=>this[Re].write(t)))),this[J]=e}setEncoding(e){this.encoding=e}get objectMode(){return this[q]}set objectMode(e){this[q]=this[q]||!!e}get async(){return this[Be]}set async(e){this[Be]=this[Be]||!!e}write(e,t,s){if(this[ke])throw new Error("write after end");if(this[U])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;typeof t=="function"&&(s=t,t="utf8"),t||(t="utf8");let i=this[Be]?fr:n=>n();return!this[q]&&!Buffer.isBuffer(e)&&(rh(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):th(e)?e=Buffer.from(e):typeof e!="string"&&(this.objectMode=!0)),this[q]?(this.flowing&&this[I]!==0&&this[$r](!0),this.flowing?this.emit("data",e):this[mi](e),this[I]!==0&&this.emit("readable"),s&&i(s),this.flowing):e.length?(typeof e=="string"&&!(t===this[J]&&!this[Re].lastNeed)&&(e=Buffer.from(e,t)),Buffer.isBuffer(e)&&this[J]&&(e=this[Re].write(e)),this.flowing&&this[I]!==0&&this[$r](!0),this.flowing?this.emit("data",e):this[mi](e),this[I]!==0&&this.emit("readable"),s&&i(s),this.flowing):(this[I]!==0&&this.emit("readable"),s&&i(s),this.flowing)}read(e){if(this[U])return null;if(this[I]===0||e===0||e>this[I])return this[_e](),null;this[q]&&(e=null),this[N].length>1&&!this[q]&&(this.encoding?this[N]=[this[N].join("")]:this[N]=[Buffer.concat(this[N],this[I])]);let t=this[$o](e||null,this[N][0]);return this[_e](),t}[$o](e,t){return e===t.length||e===null?this[di]():(this[N][0]=t.slice(e),t=t.slice(0,e),this[I]-=e),this.emit("data",t),!this[N].length&&!this[ke]&&this.emit("drain"),t}end(e,t,s){return typeof e=="function"&&(s=e,e=null),typeof t=="function"&&(s=t,t="utf8"),e&&this.write(e,t),s&&this.once("end",s),this[ke]=!0,this.writable=!1,(this.flowing||!this[hr])&&this[_e](),this}[Pt](){this[U]||(this[hr]=!1,this[Wr]=!0,this.emit("resume"),this[N].length?this[$r]():this[ke]?this[_e]():this.emit("drain"))}resume(){return this[Pt]()}pause(){this[Wr]=!1,this[hr]=!0}get destroyed(){return this[U]}get flowing(){return this[Wr]}get paused(){return this[hr]}[mi](e){this[q]?this[I]+=1:this[I]+=e.length,this[N].push(e)}[di](){return this[N].length&&(this[q]?this[I]-=1:this[I]-=this[N][0].length),this[N].shift()}[$r](e){do;while(this[Wo](this[di]()));!e&&!this[N].length&&!this[ke]&&this.emit("drain")}[Wo](e){return e?(this.emit("data",e),this.flowing):!1}pipe(e,t){if(this[U])return;let s=this[ze];return t=t||{},e===jo.stdout||e===jo.stderr?t.end=!1:t.end=t.end!==!1,t.proxyErrors=!!t.proxyErrors,s?t.end&&e.end():(this[De].push(t.proxyErrors?new Ei(this,e,t):new zr(this,e,t)),this[Be]?fr(()=>this[Pt]()):this[Pt]()),e}unpipe(e){let t=this[De].find(s=>s.dest===e);t&&(this[De].splice(this[De].indexOf(t),1),t.unpipe())}addListener(e,t){return this.on(e,t)}on(e,t){let s=super.on(e,t);return e==="data"&&!this[De].length&&!this.flowing?this[Pt]():e==="readable"&&this[I]!==0?super.emit("readable"):eh(e)&&this[ze]?(super.emit(e),this.removeAllListeners(e)):e==="error"&&this[lr]&&(this[Be]?fr(()=>t.call(this,this[lr])):t.call(this,this[lr])),s}get emittedEnd(){return this[ze]}[_e](){!this[qr]&&!this[ze]&&!this[U]&&this[N].length===0&&this[ke]&&(this[qr]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[Ur]&&this.emit("close"),this[qr]=!1)}emit(e,t,...s){if(e!=="error"&&e!=="close"&&e!==U&&this[U])return;if(e==="data")return t?this[Be]?fr(()=>this[Di](t)):this[Di](t):!1;if(e==="end")return this[zo]();if(e==="close"){if(this[Ur]=!0,!this[ze]&&!this[U])return;let n=super.emit("close");return this.removeAllListeners("close"),n}else if(e==="error"){this[lr]=t;let n=super.emit("error",t);return this[_e](),n}else if(e==="resume"){let n=super.emit("resume");return this[_e](),n}else if(e==="finish"||e==="prefinish"){let n=super.emit(e);return this.removeAllListeners(e),n}let i=super.emit(e,t,...s);return this[_e](),i}[Di](e){for(let s of this[De])s.dest.write(e)===!1&&this.pause();let t=super.emit("data",e);return this[_e](),t}[zo](){this[ze]||(this[ze]=!0,this.readable=!1,this[Be]?fr(()=>this[gi]()):this[gi]())}[gi](){if(this[Re]){let t=this[Re].end();if(t){for(let s of this[De])s.dest.write(t);super.emit("data",t)}}for(let t of this[De])t.end();let e=super.emit("end");return this.removeAllListeners("end"),e}collect(){let e=[];this[q]||(e.dataLength=0);let t=this.promise();return this.on("data",s=>{e.push(s),this[q]||(e.dataLength+=s.length)}),t.then(()=>e)}concat(){return this[q]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then(e=>this[q]?Promise.reject(new Error("cannot concat in objectMode")):this[J]?e.join(""):Buffer.concat(e,e.dataLength))}promise(){return new Promise((e,t)=>{this.on(U,()=>t(new Error("stream destroyed"))),this.on("error",s=>t(s)),this.on("end",()=>e())})}[Xl](){return{next:()=>{let t=this.read();if(t!==null)return Promise.resolve({done:!1,value:t});if(this[ke])return Promise.resolve({done:!0});let s=null,i=null,n=c=>{this.removeListener("data",o),this.removeListener("end",a),i(c)},o=c=>{this.removeListener("error",n),this.removeListener("end",a),this.pause(),s({value:c,done:!!this[ke]})},a=()=>{this.removeListener("error",n),this.removeListener("data",o),s({done:!0})},l=()=>n(new Error("stream destroyed"));return new Promise((c,h)=>{i=h,s=c,this.once(U,l),this.once("error",n),this.once("end",a),this.once("data",o)})}}}[Ql](){return{next:()=>{let t=this.read();return{value:t,done:t===null}}}}destroy(e){return this[U]?(e?this.emit("error",e):this.emit(U),this):(this[U]=!0,this[N].length=0,this[I]=0,typeof this.close=="function"&&!this[Ur]&&this.close(),e?this.emit("error",e):this.emit(U),this)}static isStream(e){return!!e&&(e instanceof Ho||e instanceof qo||e instanceof Zl&&(typeof e.pipe=="function"||typeof e.write=="function"&&typeof e.end=="function"))}}});var Yo=E((Rd,Vo)=>{u();var sh=require("zlib").constants||{ZLIB_VERNUM:4736};Vo.exports=Object.freeze(Object.assign(Object.create(null),{Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_VERSION_ERROR:-6,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,DEFLATE:1,INFLATE:2,GZIP:3,GUNZIP:4,DEFLATERAW:5,INFLATERAW:6,UNZIP:7,BROTLI_DECODE:8,BROTLI_ENCODE:9,Z_MIN_WINDOWBITS:8,Z_MAX_WINDOWBITS:15,Z_DEFAULT_WINDOWBITS:15,Z_MIN_CHUNK:64,Z_MAX_CHUNK:1/0,Z_DEFAULT_CHUNK:16384,Z_MIN_MEMLEVEL:1,Z_MAX_MEMLEVEL:9,Z_DEFAULT_MEMLEVEL:8,Z_MIN_LEVEL:-1,Z_MAX_LEVEL:9,Z_DEFAULT_LEVEL:-1,BROTLI_OPERATION_PROCESS:0,BROTLI_OPERATION_FLUSH:1,BROTLI_OPERATION_FINISH:2,BROTLI_OPERATION_EMIT_METADATA:3,BROTLI_MODE_GENERIC:0,BROTLI_MODE_TEXT:1,BROTLI_MODE_FONT:2,BROTLI_DEFAULT_MODE:0,BROTLI_MIN_QUALITY:0,BROTLI_MAX_QUALITY:11,BROTLI_DEFAULT_QUALITY:11,BROTLI_MIN_WINDOW_BITS:10,BROTLI_MAX_WINDOW_BITS:24,BROTLI_LARGE_MAX_WINDOW_BITS:30,BROTLI_DEFAULT_WINDOW:22,BROTLI_MIN_INPUT_BLOCK_BITS:16,BROTLI_MAX_INPUT_BLOCK_BITS:24,BROTLI_PARAM_MODE:0,BROTLI_PARAM_QUALITY:1,BROTLI_PARAM_LGWIN:2,BROTLI_PARAM_LGBLOCK:3,BROTLI_PARAM_DISABLE_LITERAL_CONTEXT_MODELING:4,BROTLI_PARAM_SIZE_HINT:5,BROTLI_PARAM_LARGE_WINDOW:6,BROTLI_PARAM_NPOSTFIX:7,BROTLI_PARAM_NDIRECT:8,BROTLI_DECODER_RESULT_ERROR:0,BROTLI_DECODER_RESULT_SUCCESS:1,BROTLI_DECODER_RESULT_NEEDS_MORE_INPUT:2,BROTLI_DECODER_RESULT_NEEDS_MORE_OUTPUT:3,BROTLI_DECODER_PARAM_DISABLE_RING_BUFFER_REALLOCATION:0,BROTLI_DECODER_PARAM_LARGE_WINDOW:1,BROTLI_DECODER_NO_ERROR:0,BROTLI_DECODER_SUCCESS:1,BROTLI_DECODER_NEEDS_MORE_INPUT:2,BROTLI_DECODER_NEEDS_MORE_OUTPUT:3,BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_NIBBLE:-1,BROTLI_DECODER_ERROR_FORMAT_RESERVED:-2,BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_META_NIBBLE:-3,BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_ALPHABET:-4,BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_SAME:-5,BROTLI_DECODER_ERROR_FORMAT_CL_SPACE:-6,BROTLI_DECODER_ERROR_FORMAT_HUFFMAN_SPACE:-7,BROTLI_DECODER_ERROR_FORMAT_CONTEXT_MAP_REPEAT:-8,BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_1:-9,BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_2:-10,BROTLI_DECODER_ERROR_FORMAT_TRANSFORM:-11,BROTLI_DECODER_ERROR_FORMAT_DICTIONARY:-12,BROTLI_DECODER_ERROR_FORMAT_WINDOW_BITS:-13,BROTLI_DECODER_ERROR_FORMAT_PADDING_1:-14,BROTLI_DECODER_ERROR_FORMAT_PADDING_2:-15,BROTLI_DECODER_ERROR_FORMAT_DISTANCE:-16,BROTLI_DECODER_ERROR_DICTIONARY_NOT_SET:-19,BROTLI_DECODER_ERROR_INVALID_ARGUMENTS:-20,BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MODES:-21,BROTLI_DECODER_ERROR_ALLOC_TREE_GROUPS:-22,BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MAP:-25,BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_1:-26,BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_2:-27,BROTLI_DECODER_ERROR_ALLOC_BLOCK_TYPE_TREES:-30,BROTLI_DECODER_ERROR_UNREACHABLE:-31},sh))});var bi=E((Bd,iu)=>{"use strict";u();var Ko=typeof process=="object"&&process?process:{stdout:null,stderr:null},ih=require("events"),Zo=require("stream"),Xo=require("string_decoder").StringDecoder,ve=Symbol("EOF"),xe=Symbol("maybeEmitEnd"),Ge=Symbol("emittedEnd"),Hr=Symbol("emittingEnd"),pr=Symbol("emittedError"),Jr=Symbol("closed"),Qo=Symbol("read"),Vr=Symbol("flush"),eu=Symbol("flushChunk"),V=Symbol("encoding"),Oe=Symbol("decoder"),Yr=Symbol("flowing"),mr=Symbol("paused"),Nt=Symbol("resume"),j=Symbol("bufferLength"),yi=Symbol("bufferPush"),Ai=Symbol("bufferShift"),$=Symbol("objectMode"),W=Symbol("destroyed"),Ci=Symbol("emitData"),tu=Symbol("emitEnd"),wi=Symbol("emitEnd2"),Te=Symbol("async"),dr=r=>Promise.resolve().then(r),ru=global._MP_NO_ITERATOR_SYMBOLS_!=="1",nh=ru&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),oh=ru&&Symbol.iterator||Symbol("iterator not implemented"),uh=r=>r==="end"||r==="finish"||r==="prefinish",ah=r=>r instanceof ArrayBuffer||typeof r=="object"&&r.constructor&&r.constructor.name==="ArrayBuffer"&&r.byteLength>=0,ch=r=>!Buffer.isBuffer(r)&&ArrayBuffer.isView(r),Kr=class{constructor(e,t,s){this.src=e,this.dest=t,this.opts=s,this.ondrain=()=>e[Nt](),t.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}},Fi=class extends Kr{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(e,t,s){super(e,t,s),this.proxyErrors=i=>t.emit("error",i),e.on("error",this.proxyErrors)}};iu.exports=class su extends Zo{constructor(e){super(),this[Yr]=!1,this[mr]=!1,this.pipes=[],this.buffer=[],this[$]=e&&e.objectMode||!1,this[$]?this[V]=null:this[V]=e&&e.encoding||null,this[V]==="buffer"&&(this[V]=null),this[Te]=e&&!!e.async||!1,this[Oe]=this[V]?new Xo(this[V]):null,this[ve]=!1,this[Ge]=!1,this[Hr]=!1,this[Jr]=!1,this[pr]=null,this.writable=!0,this.readable=!0,this[j]=0,this[W]=!1}get bufferLength(){return this[j]}get encoding(){return this[V]}set encoding(e){if(this[$])throw new Error("cannot set encoding in objectMode");if(this[V]&&e!==this[V]&&(this[Oe]&&this[Oe].lastNeed||this[j]))throw new Error("cannot change encoding");this[V]!==e&&(this[Oe]=e?new Xo(e):null,this.buffer.length&&(this.buffer=this.buffer.map(t=>this[Oe].write(t)))),this[V]=e}setEncoding(e){this.encoding=e}get objectMode(){return this[$]}set objectMode(e){this[$]=this[$]||!!e}get async(){return this[Te]}set async(e){this[Te]=this[Te]||!!e}write(e,t,s){if(this[ve])throw new Error("write after end");if(this[W])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;typeof t=="function"&&(s=t,t="utf8"),t||(t="utf8");let i=this[Te]?dr:n=>n();return!this[$]&&!Buffer.isBuffer(e)&&(ch(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):ah(e)?e=Buffer.from(e):typeof e!="string"&&(this.objectMode=!0)),this[$]?(this.flowing&&this[j]!==0&&this[Vr](!0),this.flowing?this.emit("data",e):this[yi](e),this[j]!==0&&this.emit("readable"),s&&i(s),this.flowing):e.length?(typeof e=="string"&&!(t===this[V]&&!this[Oe].lastNeed)&&(e=Buffer.from(e,t)),Buffer.isBuffer(e)&&this[V]&&(e=this[Oe].write(e)),this.flowing&&this[j]!==0&&this[Vr](!0),this.flowing?this.emit("data",e):this[yi](e),this[j]!==0&&this.emit("readable"),s&&i(s),this.flowing):(this[j]!==0&&this.emit("readable"),s&&i(s),this.flowing)}read(e){if(this[W])return null;if(this[j]===0||e===0||e>this[j])return this[xe](),null;this[$]&&(e=null),this.buffer.length>1&&!this[$]&&(this.encoding?this.buffer=[this.buffer.join("")]:this.buffer=[Buffer.concat(this.buffer,this[j])]);let t=this[Qo](e||null,this.buffer[0]);return this[xe](),t}[Qo](e,t){return e===t.length||e===null?this[Ai]():(this.buffer[0]=t.slice(e),t=t.slice(0,e),this[j]-=e),this.emit("data",t),!this.buffer.length&&!this[ve]&&this.emit("drain"),t}end(e,t,s){return typeof e=="function"&&(s=e,e=null),typeof t=="function"&&(s=t,t="utf8"),e&&this.write(e,t),s&&this.once("end",s),this[ve]=!0,this.writable=!1,(this.flowing||!this[mr])&&this[xe](),this}[Nt](){this[W]||(this[mr]=!1,this[Yr]=!0,this.emit("resume"),this.buffer.length?this[Vr]():this[ve]?this[xe]():this.emit("drain"))}resume(){return this[Nt]()}pause(){this[Yr]=!1,this[mr]=!0}get destroyed(){return this[W]}get flowing(){return this[Yr]}get paused(){return this[mr]}[yi](e){this[$]?this[j]+=1:this[j]+=e.length,this.buffer.push(e)}[Ai](){return this.buffer.length&&(this[$]?this[j]-=1:this[j]-=this.buffer[0].length),this.buffer.shift()}[Vr](e){do;while(this[eu](this[Ai]()));!e&&!this.buffer.length&&!this[ve]&&this.emit("drain")}[eu](e){return e?(this.emit("data",e),this.flowing):!1}pipe(e,t){if(this[W])return;let s=this[Ge];return t=t||{},e===Ko.stdout||e===Ko.stderr?t.end=!1:t.end=t.end!==!1,t.proxyErrors=!!t.proxyErrors,s?t.end&&e.end():(this.pipes.push(t.proxyErrors?new Fi(this,e,t):new Kr(this,e,t)),this[Te]?dr(()=>this[Nt]()):this[Nt]()),e}unpipe(e){let t=this.pipes.find(s=>s.dest===e);t&&(this.pipes.splice(this.pipes.indexOf(t),1),t.unpipe())}addListener(e,t){return this.on(e,t)}on(e,t){let s=super.on(e,t);return e==="data"&&!this.pipes.length&&!this.flowing?this[Nt]():e==="readable"&&this[j]!==0?super.emit("readable"):uh(e)&&this[Ge]?(super.emit(e),this.removeAllListeners(e)):e==="error"&&this[pr]&&(this[Te]?dr(()=>t.call(this,this[pr])):t.call(this,this[pr])),s}get emittedEnd(){return this[Ge]}[xe](){!this[Hr]&&!this[Ge]&&!this[W]&&this.buffer.length===0&&this[ve]&&(this[Hr]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[Jr]&&this.emit("close"),this[Hr]=!1)}emit(e,t,...s){if(e!=="error"&&e!=="close"&&e!==W&&this[W])return;if(e==="data")return t?this[Te]?dr(()=>this[Ci](t)):this[Ci](t):!1;if(e==="end")return this[tu]();if(e==="close"){if(this[Jr]=!0,!this[Ge]&&!this[W])return;let n=super.emit("close");return this.removeAllListeners("close"),n}else if(e==="error"){this[pr]=t;let n=super.emit("error",t);return this[xe](),n}else if(e==="resume"){let n=super.emit("resume");return this[xe](),n}else if(e==="finish"||e==="prefinish"){let n=super.emit(e);return this.removeAllListeners(e),n}let i=super.emit(e,t,...s);return this[xe](),i}[Ci](e){for(let s of this.pipes)s.dest.write(e)===!1&&this.pause();let t=super.emit("data",e);return this[xe](),t}[tu](){this[Ge]||(this[Ge]=!0,this.readable=!1,this[Te]?dr(()=>this[wi]()):this[wi]())}[wi](){if(this[Oe]){let t=this[Oe].end();if(t){for(let s of this.pipes)s.dest.write(t);super.emit("data",t)}}for(let t of this.pipes)t.end();let e=super.emit("end");return this.removeAllListeners("end"),e}collect(){let e=[];this[$]||(e.dataLength=0);let t=this.promise();return this.on("data",s=>{e.push(s),this[$]||(e.dataLength+=s.length)}),t.then(()=>e)}concat(){return this[$]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then(e=>this[$]?Promise.reject(new Error("cannot concat in objectMode")):this[V]?e.join(""):Buffer.concat(e,e.dataLength))}promise(){return new Promise((e,t)=>{this.on(W,()=>t(new Error("stream destroyed"))),this.on("error",s=>t(s)),this.on("end",()=>e())})}[nh](){return{next:()=>{let t=this.read();if(t!==null)return Promise.resolve({done:!1,value:t});if(this[ve])return Promise.resolve({done:!0});let s=null,i=null,n=c=>{this.removeListener("data",o),this.removeListener("end",a),i(c)},o=c=>{this.removeListener("error",n),this.removeListener("end",a),this.pause(),s({value:c,done:!!this[ve]})},a=()=>{this.removeListener("error",n),this.removeListener("data",o),s({done:!0})},l=()=>n(new Error("stream destroyed"));return new Promise((c,h)=>{i=h,s=c,this.once(W,l),this.once("error",n),this.once("end",a),this.once("data",o)})}}}[oh](){return{next:()=>{let t=this.read();return{value:t,done:t===null}}}}destroy(e){return this[W]?(e?this.emit("error",e):this.emit(W),this):(this[W]=!0,this.buffer.length=0,this[j]=0,typeof this.close=="function"&&!this[Jr]&&this.close(),e?this.emit("error",e):this.emit(W),this)}static isStream(e){return!!e&&(e instanceof su||e instanceof Zo||e instanceof ih&&(typeof e.pipe=="function"||typeof e.write=="function"&&typeof e.end=="function"))}}});var qi=E(Z=>{"use strict";u();var Bi=require("assert"),He=require("buffer").Buffer,uu=require("zlib"),ht=Z.constants=Yo(),lh=bi(),nu=He.concat,ft=Symbol("_superWrite"),Mt=class extends Error{constructor(e){super("zlib: "+e.message),this.code=e.code,this.errno=e.errno,this.code||(this.code="ZLIB_ERROR"),this.message="zlib: "+e.message,Error.captureStackTrace(this,this.constructor)}get name(){return"ZlibError"}},hh=Symbol("opts"),Dr=Symbol("flushFlag"),ou=Symbol("finishFlushFlag"),ji=Symbol("fullFlushFlag"),v=Symbol("handle"),Zr=Symbol("onError"),Lt=Symbol("sawError"),Si=Symbol("level"),ki=Symbol("strategy"),_i=Symbol("ended"),vd=Symbol("_defaultFullFlush"),Xr=class extends lh{constructor(e,t){if(!e||typeof e!="object")throw new TypeError("invalid options for ZlibBase constructor");super(e),this[Lt]=!1,this[_i]=!1,this[hh]=e,this[Dr]=e.flush,this[ou]=e.finishFlush;try{this[v]=new uu[t](e)}catch(s){throw new Mt(s)}this[Zr]=s=>{this[Lt]||(this[Lt]=!0,this.close(),this.emit("error",s))},this[v].on("error",s=>this[Zr](new Mt(s))),this.once("end",()=>this.close)}close(){this[v]&&(this[v].close(),this[v]=null,this.emit("close"))}reset(){if(!this[Lt])return Bi(this[v],"zlib binding closed"),this[v].reset()}flush(e){this.ended||(typeof e!="number"&&(e=this[ji]),this.write(Object.assign(He.alloc(0),{[Dr]:e})))}end(e,t,s){return e&&this.write(e,t),this.flush(this[ou]),this[_i]=!0,super.end(null,null,s)}get ended(){return this[_i]}write(e,t,s){if(typeof t=="function"&&(s=t,t="utf8"),typeof e=="string"&&(e=He.from(e,t)),this[Lt])return;Bi(this[v],"zlib binding closed");let i=this[v]._handle,n=i.close;i.close=()=>{};let o=this[v].close;this[v].close=()=>{},He.concat=c=>c;let a;try{let c=typeof e[Dr]=="number"?e[Dr]:this[Dr];a=this[v]._processChunk(e,c),He.concat=nu}catch(c){He.concat=nu,this[Zr](new Mt(c))}finally{this[v]&&(this[v]._handle=i,i.close=n,this[v].close=o,this[v].removeAllListeners("error"))}this[v]&&this[v].on("error",c=>this[Zr](new Mt(c)));let l;if(a)if(Array.isArray(a)&&a.length>0){l=this[ft](He.from(a[0]));for(let c=1;c<a.length;c++)l=this[ft](a[c])}else l=this[ft](He.from(a));return s&&s(),l}[ft](e){return super.write(e)}},Pe=class extends Xr{constructor(e,t){e=e||{},e.flush=e.flush||ht.Z_NO_FLUSH,e.finishFlush=e.finishFlush||ht.Z_FINISH,super(e,t),this[ji]=ht.Z_FULL_FLUSH,this[Si]=e.level,this[ki]=e.strategy}params(e,t){if(!this[Lt]){if(!this[v])throw new Error("cannot switch params when binding is closed");if(!this[v].params)throw new Error("not supported in this implementation");if(this[Si]!==e||this[ki]!==t){this.flush(ht.Z_SYNC_FLUSH),Bi(this[v],"zlib binding closed");let s=this[v].flush;this[v].flush=(i,n)=>{this.flush(i),n()};try{this[v].params(e,t)}finally{this[v].flush=s}this[v]&&(this[Si]=e,this[ki]=t)}}}},vi=class extends Pe{constructor(e){super(e,"Deflate")}},xi=class extends Pe{constructor(e){super(e,"Inflate")}},Ri=Symbol("_portable"),Oi=class extends Pe{constructor(e){super(e,"Gzip"),this[Ri]=e&&!!e.portable}[ft](e){return this[Ri]?(this[Ri]=!1,e[9]=255,super[ft](e)):super[ft](e)}},Ti=class extends Pe{constructor(e){super(e,"Gunzip")}},Pi=class extends Pe{constructor(e){super(e,"DeflateRaw")}},Ni=class extends Pe{constructor(e){super(e,"InflateRaw")}},Li=class extends Pe{constructor(e){super(e,"Unzip")}},Qr=class extends Xr{constructor(e,t){e=e||{},e.flush=e.flush||ht.BROTLI_OPERATION_PROCESS,e.finishFlush=e.finishFlush||ht.BROTLI_OPERATION_FINISH,super(e,t),this[ji]=ht.BROTLI_OPERATION_FLUSH}},Mi=class extends Qr{constructor(e){super(e,"BrotliCompress")}},Ii=class extends Qr{constructor(e){super(e,"BrotliDecompress")}};Z.Deflate=vi;Z.Inflate=xi;Z.Gzip=Oi;Z.Gunzip=Ti;Z.DeflateRaw=Pi;Z.InflateRaw=Ni;Z.Unzip=Li;typeof uu.BrotliCompress=="function"?(Z.BrotliCompress=Mi,Z.BrotliDecompress=Ii):Z.BrotliCompress=Z.BrotliDecompress=class{constructor(){throw new Error("Brotli is not supported in this version of Node.js")}}});var It=E((Td,au)=>{u();var fh=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform;au.exports=fh!=="win32"?r=>r:r=>r&&r.replace(/\\/g,"/")});var es=E((Nd,cu)=>{"use strict";u();var ph=Gr(),Ui=It(),$i=Symbol("slurp");cu.exports=class extends ph{constructor(e,t,s){switch(super(),this.pause(),this.extended=t,this.globalExtended=s,this.header=e,this.startBlockSize=512*Math.ceil(e.size/512),this.blockRemain=this.startBlockSize,this.remain=e.size,this.type=e.type,this.meta=!1,this.ignore=!1,this.type){case"File":case"OldFile":case"Link":case"SymbolicLink":case"CharacterDevice":case"BlockDevice":case"Directory":case"FIFO":case"ContiguousFile":case"GNUDumpDir":break;case"NextFileHasLongLinkpath":case"NextFileHasLongPath":case"OldGnuLongPath":case"GlobalExtendedHeader":case"ExtendedHeader":case"OldExtendedHeader":this.meta=!0;break;default:this.ignore=!0}this.path=Ui(e.path),this.mode=e.mode,this.mode&&(this.mode=this.mode&4095),this.uid=e.uid,this.gid=e.gid,this.uname=e.uname,this.gname=e.gname,this.size=e.size,this.mtime=e.mtime,this.atime=e.atime,this.ctime=e.ctime,this.linkpath=Ui(e.linkpath),this.uname=e.uname,this.gname=e.gname,t&&this[$i](t),s&&this[$i](s,!0)}write(e){let t=e.length;if(t>this.blockRemain)throw new Error("writing more to entry than is appropriate");let s=this.remain,i=this.blockRemain;return this.remain=Math.max(0,s-t),this.blockRemain=Math.max(0,i-t),this.ignore?!0:s>=t?super.write(e):super.write(e.slice(0,s))}[$i](e,t){for(let s in e)e[s]!==null&&e[s]!==void 0&&!(t&&s==="path")&&(this[s]=s==="path"||s==="linkpath"?Ui(e[s]):e[s])}}});var Wi=E(ts=>{"use strict";u();ts.name=new Map([["0","File"],["","OldFile"],["1","Link"],["2","SymbolicLink"],["3","CharacterDevice"],["4","BlockDevice"],["5","Directory"],["6","FIFO"],["7","ContiguousFile"],["g","GlobalExtendedHeader"],["x","ExtendedHeader"],["A","SolarisACL"],["D","GNUDumpDir"],["I","Inode"],["K","NextFileHasLongLinkpath"],["L","NextFileHasLongPath"],["M","ContinuationFile"],["N","OldGnuLongPath"],["S","SparseFile"],["V","TapeVolumeHeader"],["X","OldExtendedHeader"]]);ts.code=new Map(Array.from(ts.name).map(r=>[r[1],r[0]]))});var pu=E((Md,fu)=>{"use strict";u();var mh=(r,e)=>{if(Number.isSafeInteger(r))r<0?Dh(r,e):dh(r,e);else throw Error("cannot encode number outside of javascript safe integer range");return e},dh=(r,e)=>{e[0]=128;for(var t=e.length;t>1;t--)e[t-1]=r&255,r=Math.floor(r/256)},Dh=(r,e)=>{e[0]=255;var t=!1;r=r*-1;for(var s=e.length;s>1;s--){var i=r&255;r=Math.floor(r/256),t?e[s-1]=lu(i):i===0?e[s-1]=0:(t=!0,e[s-1]=hu(i))}},gh=r=>{let e=r[0],t=e===128?yh(r.slice(1,r.length)):e===255?Eh(r):null;if(t===null)throw Error("invalid base256 encoding");if(!Number.isSafeInteger(t))throw Error("parsed number outside of javascript safe integer range");return t},Eh=r=>{for(var e=r.length,t=0,s=!1,i=e-1;i>-1;i--){var n=r[i],o;s?o=lu(n):n===0?o=n:(s=!0,o=hu(n)),o!==0&&(t-=o*Math.pow(256,e-i-1))}return t},yh=r=>{for(var e=r.length,t=0,s=e-1;s>-1;s--){var i=r[s];i!==0&&(t+=i*Math.pow(256,e-s-1))}return t},lu=r=>(255^r)&255,hu=r=>(255^r)+1&255;fu.exports={encode:mh,parse:gh}});var qt=E((Id,du)=>{"use strict";u();var zi=Wi(),jt=require("path").posix,mu=pu(),Gi=Symbol("slurp"),X=Symbol("type"),Vi=class{constructor(e,t,s,i){this.cksumValid=!1,this.needPax=!1,this.nullBlock=!1,this.block=null,this.path=null,this.mode=null,this.uid=null,this.gid=null,this.size=null,this.mtime=null,this.cksum=null,this[X]="0",this.linkpath=null,this.uname=null,this.gname=null,this.devmaj=0,this.devmin=0,this.atime=null,this.ctime=null,Buffer.isBuffer(e)?this.decode(e,t||0,s,i):e&&this.set(e)}decode(e,t,s,i){if(t||(t=0),!e||!(e.length>=t+512))throw new Error("need 512 bytes for header");if(this.path=pt(e,t,100),this.mode=Je(e,t+100,8),this.uid=Je(e,t+108,8),this.gid=Je(e,t+116,8),this.size=Je(e,t+124,12),this.mtime=Hi(e,t+136,12),this.cksum=Je(e,t+148,12),this[Gi](s),this[Gi](i,!0),this[X]=pt(e,t+156,1),this[X]===""&&(this[X]="0"),this[X]==="0"&&this.path.slice(-1)==="/"&&(this[X]="5"),this[X]==="5"&&(this.size=0),this.linkpath=pt(e,t+157,100),e.slice(t+257,t+265).toString()==="ustar\x0000")if(this.uname=pt(e,t+265,32),this.gname=pt(e,t+297,32),this.devmaj=Je(e,t+329,8),this.devmin=Je(e,t+337,8),e[t+475]!==0){let o=pt(e,t+345,155);this.path=o+"/"+this.path}else{let o=pt(e,t+345,130);o&&(this.path=o+"/"+this.path),this.atime=Hi(e,t+476,12),this.ctime=Hi(e,t+488,12)}let n=8*32;for(let o=t;o<t+148;o++)n+=e[o];for(let o=t+156;o<t+512;o++)n+=e[o];this.cksumValid=n===this.cksum,this.cksum===null&&n===8*32&&(this.nullBlock=!0)}[Gi](e,t){for(let s in e)e[s]!==null&&e[s]!==void 0&&!(t&&s==="path")&&(this[s]=e[s])}encode(e,t){if(e||(e=this.block=Buffer.alloc(512),t=0),t||(t=0),!(e.length>=t+512))throw new Error("need 512 bytes for header");let s=this.ctime||this.atime?130:155,i=Ah(this.path||"",s),n=i[0],o=i[1];this.needPax=i[2],this.needPax=mt(e,t,100,n)||this.needPax,this.needPax=Ve(e,t+100,8,this.mode)||this.needPax,this.needPax=Ve(e,t+108,8,this.uid)||this.needPax,this.needPax=Ve(e,t+116,8,this.gid)||this.needPax,this.needPax=Ve(e,t+124,12,this.size)||this.needPax,this.needPax=Ji(e,t+136,12,this.mtime)||this.needPax,e[t+156]=this[X].charCodeAt(0),this.needPax=mt(e,t+157,100,this.linkpath)||this.needPax,e.write("ustar\x0000",t+257,8),this.needPax=mt(e,t+265,32,this.uname)||this.needPax,this.needPax=mt(e,t+297,32,this.gname)||this.needPax,this.needPax=Ve(e,t+329,8,this.devmaj)||this.needPax,this.needPax=Ve(e,t+337,8,this.devmin)||this.needPax,this.needPax=mt(e,t+345,s,o)||this.needPax,e[t+475]!==0?this.needPax=mt(e,t+345,155,o)||this.needPax:(this.needPax=mt(e,t+345,130,o)||this.needPax,this.needPax=Ji(e,t+476,12,this.atime)||this.needPax,this.needPax=Ji(e,t+488,12,this.ctime)||this.needPax);let a=8*32;for(let l=t;l<t+148;l++)a+=e[l];for(let l=t+156;l<t+512;l++)a+=e[l];return this.cksum=a,Ve(e,t+148,8,this.cksum),this.cksumValid=!0,this.needPax}set(e){for(let t in e)e[t]!==null&&e[t]!==void 0&&(this[t]=e[t])}get type(){return zi.name.get(this[X])||this[X]}get typeKey(){return this[X]}set type(e){zi.code.has(e)?this[X]=zi.code.get(e):this[X]=e}},Ah=(r,e)=>{let s=r,i="",n,o=jt.parse(r).root||".";if(Buffer.byteLength(s)<100)n=[s,i,!1];else{i=jt.dirname(s),s=jt.basename(s);do Buffer.byteLength(s)<=100&&Buffer.byteLength(i)<=e?n=[s,i,!1]:Buffer.byteLength(s)>100&&Buffer.byteLength(i)<=e?n=[s.slice(0,100-1),i,!0]:(s=jt.join(jt.basename(i),s),i=jt.dirname(i));while(i!==o&&!n);n||(n=[r.slice(0,100-1),"",!0])}return n},pt=(r,e,t)=>r.slice(e,e+t).toString("utf8").replace(/\0.*/,""),Hi=(r,e,t)=>Ch(Je(r,e,t)),Ch=r=>r===null?null:new Date(r*1e3),Je=(r,e,t)=>r[e]&128?mu.parse(r.slice(e,e+t)):Fh(r,e,t),wh=r=>isNaN(r)?null:r,Fh=(r,e,t)=>wh(parseInt(r.slice(e,e+t).toString("utf8").replace(/\0.*$/,"").trim(),8)),bh={12:8589934591,8:2097151},Ve=(r,e,t,s)=>s===null?!1:s>bh[t]||s<0?(mu.encode(s,r.slice(e,e+t)),!0):(Sh(r,e,t,s),!1),Sh=(r,e,t,s)=>r.write(kh(s,t),e,t,"ascii"),kh=(r,e)=>_h(Math.floor(r).toString(8),e),_h=(r,e)=>(r.length===e-1?r:new Array(e-r.length-1).join("0")+r+" ")+"\0",Ji=(r,e,t,s)=>s===null?!1:Ve(r,e,t,s.getTime()/1e3),Rh=new Array(156).join("\0"),mt=(r,e,t,s)=>s===null?!1:(r.write(s+Rh,e,t,"utf8"),s.length!==Buffer.byteLength(s)||s.length>t);du.exports=Vi});var rs=E((jd,Du)=>{"use strict";u();var Bh=qt(),vh=require("path"),gr=class{constructor(e,t){this.atime=e.atime||null,this.charset=e.charset||null,this.comment=e.comment||null,this.ctime=e.ctime||null,this.gid=e.gid||null,this.gname=e.gname||null,this.linkpath=e.linkpath||null,this.mtime=e.mtime||null,this.path=e.path||null,this.size=e.size||null,this.uid=e.uid||null,this.uname=e.uname||null,this.dev=e.dev||null,this.ino=e.ino||null,this.nlink=e.nlink||null,this.global=t||!1}encode(){let e=this.encodeBody();if(e==="")return null;let t=Buffer.byteLength(e),s=512*Math.ceil(1+t/512),i=Buffer.allocUnsafe(s);for(let n=0;n<512;n++)i[n]=0;new Bh({path:("PaxHeader/"+vh.basename(this.path)).slice(0,99),mode:this.mode||420,uid:this.uid||null,gid:this.gid||null,size:t,mtime:this.mtime||null,type:this.global?"GlobalExtendedHeader":"ExtendedHeader",linkpath:"",uname:this.uname||"",gname:this.gname||"",devmaj:0,devmin:0,atime:this.atime||null,ctime:this.ctime||null}).encode(i),i.write(e,512,t,"utf8");for(let n=t+512;n<i.length;n++)i[n]=0;return i}encodeBody(){return this.encodeField("path")+this.encodeField("ctime")+this.encodeField("atime")+this.encodeField("dev")+this.encodeField("ino")+this.encodeField("nlink")+this.encodeField("charset")+this.encodeField("comment")+this.encodeField("gid")+this.encodeField("gname")+this.encodeField("linkpath")+this.encodeField("mtime")+this.encodeField("size")+this.encodeField("uid")+this.encodeField("uname")}encodeField(e){if(this[e]===null||this[e]===void 0)return"";let t=this[e]instanceof Date?this[e].getTime()/1e3:this[e],s=" "+(e==="dev"||e==="ino"||e==="nlink"?"SCHILY.":"")+e+"="+t+`
`,i=Buffer.byteLength(s),n=Math.floor(Math.log(i)/Math.log(10))+1;return i+n>=Math.pow(10,n)&&(n+=1),n+i+s}};gr.parse=(r,e,t)=>new gr(xh(Oh(r),e),t);var xh=(r,e)=>e?Object.keys(r).reduce((t,s)=>(t[s]=r[s],t),e):r,Oh=r=>r.replace(/\n$/,"").split(`
`).reduce(Th,Object.create(null)),Th=(r,e)=>{let t=parseInt(e,10);if(t!==Buffer.byteLength(e)+1)return r;e=e.slice((t+" ").length);let s=e.split("="),i=s.shift().replace(/^SCHILY\.(dev|ino|nlink)/,"$1");if(!i)return r;let n=s.join("=");return r[i]=/^([A-Z]+\.)?([mac]|birth|creation)time$/.test(i)?new Date(n*1e3):/^[0-9]+$/.test(n)?+n:n,r};Du.exports=gr});var Ut=E((qd,gu)=>{u();gu.exports=r=>{let e=r.length-1,t=-1;for(;e>-1&&r.charAt(e)==="/";)t=e,e--;return t===-1?r:r.slice(0,t)}});var ss=E((Ud,Eu)=>{"use strict";u();Eu.exports=r=>class extends r{warn(e,t,s={}){this.file&&(s.file=this.file),this.cwd&&(s.cwd=this.cwd),s.code=t instanceof Error&&t.code||e,s.tarCode=e,!this.strict&&s.recoverable!==!1?(t instanceof Error&&(s=Object.assign(t,s),t=t.message),this.emit("warn",s.tarCode,t,s)):t instanceof Error?this.emit("error",Object.assign(t,s)):this.emit("error",Object.assign(new Error(`${e}: ${t}`),s))}}});var Ki=E((Wd,yu)=>{"use strict";u();var is=["|","<",">","?",":"],Yi=is.map(r=>String.fromCharCode(61440+r.charCodeAt(0))),Ph=new Map(is.map((r,e)=>[r,Yi[e]])),Nh=new Map(Yi.map((r,e)=>[r,is[e]]));yu.exports={encode:r=>is.reduce((e,t)=>e.split(t).join(Ph.get(t)),r),decode:r=>Yi.reduce((e,t)=>e.split(t).join(Nh.get(t)),r)}});var Zi=E((zd,Cu)=>{u();var{isAbsolute:Lh,parse:Au}=require("path").win32;Cu.exports=r=>{let e="",t=Au(r);for(;Lh(r)||t.root;){let s=r.charAt(0)==="/"&&r.slice(0,4)!=="//?/"?"/":t.root;r=r.slice(s.length),e+=s,t=Au(r)}return[e,r]}});var Fu=E((Gd,wu)=>{"use strict";u();wu.exports=(r,e,t)=>(r&=4095,t&&(r=(r|384)&-19),e&&(r&256&&(r|=64),r&32&&(r|=8),r&4&&(r|=1)),r)});var an=E((Vd,Mu)=>{"use strict";u();var vu=Gr(),xu=rs(),Ou=qt(),Ee=require("fs"),bu=require("path"),ge=It(),Mh=Ut(),Tu=(r,e)=>e?(r=ge(r).replace(/^\.(\/|$)/,""),Mh(e)+"/"+r):ge(r),Ih=16*1024*1024,Su=Symbol("process"),ku=Symbol("file"),_u=Symbol("directory"),Qi=Symbol("symlink"),Ru=Symbol("hardlink"),Er=Symbol("header"),ns=Symbol("read"),en=Symbol("lstat"),os=Symbol("onlstat"),tn=Symbol("onread"),rn=Symbol("onreadlink"),sn=Symbol("openfile"),nn=Symbol("onopenfile"),Ye=Symbol("close"),us=Symbol("mode"),on=Symbol("awaitDrain"),Xi=Symbol("ondrain"),ye=Symbol("prefix"),Bu=Symbol("hadError"),Pu=ss(),jh=Ki(),Nu=Zi(),Lu=Fu(),as=Pu(class extends vu{constructor(e,t){if(t=t||{},super(t),typeof e!="string")throw new TypeError("path is required");this.path=ge(e),this.portable=!!t.portable,this.myuid=process.getuid&&process.getuid()||0,this.myuser=process.env.USER||"",this.maxReadSize=t.maxReadSize||Ih,this.linkCache=t.linkCache||new Map,this.statCache=t.statCache||new Map,this.preservePaths=!!t.preservePaths,this.cwd=ge(t.cwd||process.cwd()),this.strict=!!t.strict,this.noPax=!!t.noPax,this.noMtime=!!t.noMtime,this.mtime=t.mtime||null,this.prefix=t.prefix?ge(t.prefix):null,this.fd=null,this.blockLen=null,this.blockRemain=null,this.buf=null,this.offset=null,this.length=null,this.pos=null,this.remain=null,typeof t.onwarn=="function"&&this.on("warn",t.onwarn);let s=!1;if(!this.preservePaths){let[i,n]=Nu(this.path);i&&(this.path=n,s=i)}this.win32=!!t.win32||process.platform==="win32",this.win32&&(this.path=jh.decode(this.path.replace(/\\/g,"/")),e=e.replace(/\\/g,"/")),this.absolute=ge(t.absolute||bu.resolve(this.cwd,e)),this.path===""&&(this.path="./"),s&&this.warn("TAR_ENTRY_INFO",`stripping ${s} from absolute path`,{entry:this,path:s+this.path}),this.statCache.has(this.absolute)?this[os](this.statCache.get(this.absolute)):this[en]()}emit(e,...t){return e==="error"&&(this[Bu]=!0),super.emit(e,...t)}[en](){Ee.lstat(this.absolute,(e,t)=>{if(e)return this.emit("error",e);this[os](t)})}[os](e){this.statCache.set(this.absolute,e),this.stat=e,e.isFile()||(e.size=0),this.type=Uh(e),this.emit("stat",e),this[Su]()}[Su](){switch(this.type){case"File":return this[ku]();case"Directory":return this[_u]();case"SymbolicLink":return this[Qi]();default:return this.end()}}[us](e){return Lu(e,this.type==="Directory",this.portable)}[ye](e){return Tu(e,this.prefix)}[Er](){this.type==="Directory"&&this.portable&&(this.noMtime=!0),this.header=new Ou({path:this[ye](this.path),linkpath:this.type==="Link"?this[ye](this.linkpath):this.linkpath,mode:this[us](this.stat.mode),uid:this.portable?null:this.stat.uid,gid:this.portable?null:this.stat.gid,size:this.stat.size,mtime:this.noMtime?null:this.mtime||this.stat.mtime,type:this.type,uname:this.portable?null:this.stat.uid===this.myuid?this.myuser:"",atime:this.portable?null:this.stat.atime,ctime:this.portable?null:this.stat.ctime}),this.header.encode()&&!this.noPax&&super.write(new xu({atime:this.portable?null:this.header.atime,ctime:this.portable?null:this.header.ctime,gid:this.portable?null:this.header.gid,mtime:this.noMtime?null:this.mtime||this.header.mtime,path:this[ye](this.path),linkpath:this.type==="Link"?this[ye](this.linkpath):this.linkpath,size:this.header.size,uid:this.portable?null:this.header.uid,uname:this.portable?null:this.header.uname,dev:this.portable?null:this.stat.dev,ino:this.portable?null:this.stat.ino,nlink:this.portable?null:this.stat.nlink}).encode()),super.write(this.header.block)}[_u](){this.path.slice(-1)!=="/"&&(this.path+="/"),this.stat.size=0,this[Er](),this.end()}[Qi](){Ee.readlink(this.absolute,(e,t)=>{if(e)return this.emit("error",e);this[rn](t)})}[rn](e){this.linkpath=ge(e),this[Er](),this.end()}[Ru](e){this.type="Link",this.linkpath=ge(bu.relative(this.cwd,e)),this.stat.size=0,this[Er](),this.end()}[ku](){if(this.stat.nlink>1){let e=this.stat.dev+":"+this.stat.ino;if(this.linkCache.has(e)){let t=this.linkCache.get(e);if(t.indexOf(this.cwd)===0)return this[Ru](t)}this.linkCache.set(e,this.absolute)}if(this[Er](),this.stat.size===0)return this.end();this[sn]()}[sn](){Ee.open(this.absolute,"r",(e,t)=>{if(e)return this.emit("error",e);this[nn](t)})}[nn](e){if(this.fd=e,this[Bu])return this[Ye]();this.blockLen=512*Math.ceil(this.stat.size/512),this.blockRemain=this.blockLen;let t=Math.min(this.blockLen,this.maxReadSize);this.buf=Buffer.allocUnsafe(t),this.offset=0,this.pos=0,this.remain=this.stat.size,this.length=this.buf.length,this[ns]()}[ns](){let{fd:e,buf:t,offset:s,length:i,pos:n}=this;Ee.read(e,t,s,i,n,(o,a)=>{if(o)return this[Ye](()=>this.emit("error",o));this[tn](a)})}[Ye](e){Ee.close(this.fd,e)}[tn](e){if(e<=0&&this.remain>0){let i=new Error("encountered unexpected EOF");return i.path=this.absolute,i.syscall="read",i.code="EOF",this[Ye](()=>this.emit("error",i))}if(e>this.remain){let i=new Error("did not encounter expected EOF");return i.path=this.absolute,i.syscall="read",i.code="EOF",this[Ye](()=>this.emit("error",i))}if(e===this.remain)for(let i=e;i<this.length&&e<this.blockRemain;i++)this.buf[i+this.offset]=0,e++,this.remain++;let t=this.offset===0&&e===this.buf.length?this.buf:this.buf.slice(this.offset,this.offset+e);this.write(t)?this[Xi]():this[on](()=>this[Xi]())}[on](e){this.once("drain",e)}write(e){if(this.blockRemain<e.length){let t=new Error("writing more data than expected");return t.path=this.absolute,this.emit("error",t)}return this.remain-=e.length,this.blockRemain-=e.length,this.pos+=e.length,this.offset+=e.length,super.write(e)}[Xi](){if(!this.remain)return this.blockRemain&&super.write(Buffer.alloc(this.blockRemain)),this[Ye](e=>e?this.emit("error",e):this.end());this.offset>=this.length&&(this.buf=Buffer.allocUnsafe(Math.min(this.blockRemain,this.buf.length)),this.offset=0),this.length=this.buf.length-this.offset,this[ns]()}}),un=class extends as{[en](){this[os](Ee.lstatSync(this.absolute))}[Qi](){this[rn](Ee.readlinkSync(this.absolute))}[sn](){this[nn](Ee.openSync(this.absolute,"r"))}[ns](){let e=!0;try{let{fd:t,buf:s,offset:i,length:n,pos:o}=this,a=Ee.readSync(t,s,i,n,o);this[tn](a),e=!1}finally{if(e)try{this[Ye](()=>{})}catch{}}}[on](e){e()}[Ye](e){Ee.closeSync(this.fd),e()}},qh=Pu(class extends vu{constructor(e,t){t=t||{},super(t),this.preservePaths=!!t.preservePaths,this.portable=!!t.portable,this.strict=!!t.strict,this.noPax=!!t.noPax,this.noMtime=!!t.noMtime,this.readEntry=e,this.type=e.type,this.type==="Directory"&&this.portable&&(this.noMtime=!0),this.prefix=t.prefix||null,this.path=ge(e.path),this.mode=this[us](e.mode),this.uid=this.portable?null:e.uid,this.gid=this.portable?null:e.gid,this.uname=this.portable?null:e.uname,this.gname=this.portable?null:e.gname,this.size=e.size,this.mtime=this.noMtime?null:t.mtime||e.mtime,this.atime=this.portable?null:e.atime,this.ctime=this.portable?null:e.ctime,this.linkpath=ge(e.linkpath),typeof t.onwarn=="function"&&this.on("warn",t.onwarn);let s=!1;if(!this.preservePaths){let[i,n]=Nu(this.path);i&&(this.path=n,s=i)}this.remain=e.size,this.blockRemain=e.startBlockSize,this.header=new Ou({path:this[ye](this.path),linkpath:this.type==="Link"?this[ye](this.linkpath):this.linkpath,mode:this.mode,uid:this.portable?null:this.uid,gid:this.portable?null:this.gid,size:this.size,mtime:this.noMtime?null:this.mtime,type:this.type,uname:this.portable?null:this.uname,atime:this.portable?null:this.atime,ctime:this.portable?null:this.ctime}),s&&this.warn("TAR_ENTRY_INFO",`stripping ${s} from absolute path`,{entry:this,path:s+this.path}),this.header.encode()&&!this.noPax&&super.write(new xu({atime:this.portable?null:this.atime,ctime:this.portable?null:this.ctime,gid:this.portable?null:this.gid,mtime:this.noMtime?null:this.mtime,path:this[ye](this.path),linkpath:this.type==="Link"?this[ye](this.linkpath):this.linkpath,size:this.size,uid:this.portable?null:this.uid,uname:this.portable?null:this.uname,dev:this.portable?null:this.readEntry.dev,ino:this.portable?null:this.readEntry.ino,nlink:this.portable?null:this.readEntry.nlink}).encode()),super.write(this.header.block),e.pipe(this)}[ye](e){return Tu(e,this.prefix)}[us](e){return Lu(e,this.type==="Directory",this.portable)}write(e){let t=e.length;if(t>this.blockRemain)throw new Error("writing more to entry than is appropriate");return this.blockRemain-=t,super.write(e)}end(){return this.blockRemain&&super.write(Buffer.alloc(this.blockRemain)),super.end()}});as.Sync=un;as.Tar=qh;var Uh=r=>r.isFile()?"File":r.isDirectory()?"Directory":r.isSymbolicLink()?"SymbolicLink":"Unsupported";Mu.exports=as});var ju=E((Yd,Iu)=>{"use strict";u();Iu.exports=function(r){r.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}});var cn=E((Kd,qu)=>{"use strict";u();qu.exports=k;k.Node=dt;k.create=k;function k(r){var e=this;if(e instanceof k||(e=new k),e.tail=null,e.head=null,e.length=0,r&&typeof r.forEach=="function")r.forEach(function(i){e.push(i)});else if(arguments.length>0)for(var t=0,s=arguments.length;t<s;t++)e.push(arguments[t]);return e}k.prototype.removeNode=function(r){if(r.list!==this)throw new Error("removing node which does not belong to this list");var e=r.next,t=r.prev;return e&&(e.prev=t),t&&(t.next=e),r===this.head&&(this.head=e),r===this.tail&&(this.tail=t),r.list.length--,r.next=null,r.prev=null,r.list=null,e};k.prototype.unshiftNode=function(r){if(r!==this.head){r.list&&r.list.removeNode(r);var e=this.head;r.list=this,r.next=e,e&&(e.prev=r),this.head=r,this.tail||(this.tail=r),this.length++}};k.prototype.pushNode=function(r){if(r!==this.tail){r.list&&r.list.removeNode(r);var e=this.tail;r.list=this,r.prev=e,e&&(e.next=r),this.tail=r,this.head||(this.head=r),this.length++}};k.prototype.push=function(){for(var r=0,e=arguments.length;r<e;r++)Wh(this,arguments[r]);return this.length};k.prototype.unshift=function(){for(var r=0,e=arguments.length;r<e;r++)zh(this,arguments[r]);return this.length};k.prototype.pop=function(){if(!!this.tail){var r=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,r}};k.prototype.shift=function(){if(!!this.head){var r=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,r}};k.prototype.forEach=function(r,e){e=e||this;for(var t=this.head,s=0;t!==null;s++)r.call(e,t.value,s,this),t=t.next};k.prototype.forEachReverse=function(r,e){e=e||this;for(var t=this.tail,s=this.length-1;t!==null;s--)r.call(e,t.value,s,this),t=t.prev};k.prototype.get=function(r){for(var e=0,t=this.head;t!==null&&e<r;e++)t=t.next;if(e===r&&t!==null)return t.value};k.prototype.getReverse=function(r){for(var e=0,t=this.tail;t!==null&&e<r;e++)t=t.prev;if(e===r&&t!==null)return t.value};k.prototype.map=function(r,e){e=e||this;for(var t=new k,s=this.head;s!==null;)t.push(r.call(e,s.value,this)),s=s.next;return t};k.prototype.mapReverse=function(r,e){e=e||this;for(var t=new k,s=this.tail;s!==null;)t.push(r.call(e,s.value,this)),s=s.prev;return t};k.prototype.reduce=function(r,e){var t,s=this.head;if(arguments.length>1)t=e;else if(this.head)s=this.head.next,t=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var i=0;s!==null;i++)t=r(t,s.value,i),s=s.next;return t};k.prototype.reduceReverse=function(r,e){var t,s=this.tail;if(arguments.length>1)t=e;else if(this.tail)s=this.tail.prev,t=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var i=this.length-1;s!==null;i--)t=r(t,s.value,i),s=s.prev;return t};k.prototype.toArray=function(){for(var r=new Array(this.length),e=0,t=this.head;t!==null;e++)r[e]=t.value,t=t.next;return r};k.prototype.toArrayReverse=function(){for(var r=new Array(this.length),e=0,t=this.tail;t!==null;e++)r[e]=t.value,t=t.prev;return r};k.prototype.slice=function(r,e){e=e||this.length,e<0&&(e+=this.length),r=r||0,r<0&&(r+=this.length);var t=new k;if(e<r||e<0)return t;r<0&&(r=0),e>this.length&&(e=this.length);for(var s=0,i=this.head;i!==null&&s<r;s++)i=i.next;for(;i!==null&&s<e;s++,i=i.next)t.push(i.value);return t};k.prototype.sliceReverse=function(r,e){e=e||this.length,e<0&&(e+=this.length),r=r||0,r<0&&(r+=this.length);var t=new k;if(e<r||e<0)return t;r<0&&(r=0),e>this.length&&(e=this.length);for(var s=this.length,i=this.tail;i!==null&&s>e;s--)i=i.prev;for(;i!==null&&s>r;s--,i=i.prev)t.push(i.value);return t};k.prototype.splice=function(r,e,...t){r>this.length&&(r=this.length-1),r<0&&(r=this.length+r);for(var s=0,i=this.head;i!==null&&s<r;s++)i=i.next;for(var n=[],s=0;i&&s<e;s++)n.push(i.value),i=this.removeNode(i);i===null&&(i=this.tail),i!==this.head&&i!==this.tail&&(i=i.prev);for(var s=0;s<t.length;s++)i=$h(this,i,t[s]);return n};k.prototype.reverse=function(){for(var r=this.head,e=this.tail,t=r;t!==null;t=t.prev){var s=t.prev;t.prev=t.next,t.next=s}return this.head=e,this.tail=r,this};function $h(r,e,t){var s=e===r.head?new dt(t,null,e,r):new dt(t,e,e.next,r);return s.next===null&&(r.tail=s),s.prev===null&&(r.head=s),r.length++,s}function Wh(r,e){r.tail=new dt(e,r.tail,null,r),r.head||(r.head=r.tail),r.length++}function zh(r,e){r.head=new dt(e,null,r.head,r),r.tail||(r.tail=r.head),r.length++}function dt(r,e,t,s){if(!(this instanceof dt))return new dt(r,e,t,s);this.list=s,this.value=r,e?(e.next=this,this.prev=e):this.prev=null,t?(t.prev=this,this.next=t):this.next=null}try{ju()(k)}catch{}});var gs=E((Xd,Ju)=>{"use strict";u();var ds=class{constructor(e,t){this.path=e||"./",this.absolute=t,this.entry=null,this.stat=null,this.readdir=null,this.pending=!1,this.ignore=!1,this.piped=!1}},Gh=Gr(),Hh=qi(),Jh=es(),En=an(),Vh=En.Sync,Yh=En.Tar,Kh=cn(),Uu=Buffer.alloc(1024),hs=Symbol("onStat"),cs=Symbol("ended"),Ae=Symbol("queue"),$t=Symbol("current"),Dt=Symbol("process"),ls=Symbol("processing"),$u=Symbol("processJob"),Ce=Symbol("jobs"),ln=Symbol("jobDone"),fs=Symbol("addFSEntry"),Wu=Symbol("addTarEntry"),mn=Symbol("stat"),dn=Symbol("readdir"),ps=Symbol("onreaddir"),ms=Symbol("pipe"),zu=Symbol("entry"),hn=Symbol("entryOpt"),Dn=Symbol("writeEntryClass"),Hu=Symbol("write"),fn=Symbol("ondrain"),Ds=require("fs"),Gu=require("path"),Zh=ss(),pn=It(),yn=Zh(class extends Gh{constructor(e){super(e),e=e||Object.create(null),this.opt=e,this.file=e.file||"",this.cwd=e.cwd||process.cwd(),this.maxReadSize=e.maxReadSize,this.preservePaths=!!e.preservePaths,this.strict=!!e.strict,this.noPax=!!e.noPax,this.prefix=pn(e.prefix||""),this.linkCache=e.linkCache||new Map,this.statCache=e.statCache||new Map,this.readdirCache=e.readdirCache||new Map,this[Dn]=En,typeof e.onwarn=="function"&&this.on("warn",e.onwarn),this.portable=!!e.portable,this.zip=null,e.gzip?(typeof e.gzip!="object"&&(e.gzip={}),this.portable&&(e.gzip.portable=!0),this.zip=new Hh.Gzip(e.gzip),this.zip.on("data",t=>super.write(t)),this.zip.on("end",t=>super.end()),this.zip.on("drain",t=>this[fn]()),this.on("resume",t=>this.zip.resume())):this.on("drain",this[fn]),this.noDirRecurse=!!e.noDirRecurse,this.follow=!!e.follow,this.noMtime=!!e.noMtime,this.mtime=e.mtime||null,this.filter=typeof e.filter=="function"?e.filter:t=>!0,this[Ae]=new Kh,this[Ce]=0,this.jobs=+e.jobs||4,this[ls]=!1,this[cs]=!1}[Hu](e){return super.write(e)}add(e){return this.write(e),this}end(e){return e&&this.write(e),this[cs]=!0,this[Dt](),this}write(e){if(this[cs])throw new Error("write after end");return e instanceof Jh?this[Wu](e):this[fs](e),this.flowing}[Wu](e){let t=pn(Gu.resolve(this.cwd,e.path));if(!this.filter(e.path,e))e.resume();else{let s=new ds(e.path,t,!1);s.entry=new Yh(e,this[hn](s)),s.entry.on("end",i=>this[ln](s)),this[Ce]+=1,this[Ae].push(s)}this[Dt]()}[fs](e){let t=pn(Gu.resolve(this.cwd,e));this[Ae].push(new ds(e,t)),this[Dt]()}[mn](e){e.pending=!0,this[Ce]+=1;let t=this.follow?"stat":"lstat";Ds[t](e.absolute,(s,i)=>{e.pending=!1,this[Ce]-=1,s?this.emit("error",s):this[hs](e,i)})}[hs](e,t){this.statCache.set(e.absolute,t),e.stat=t,this.filter(e.path,t)||(e.ignore=!0),this[Dt]()}[dn](e){e.pending=!0,this[Ce]+=1,Ds.readdir(e.absolute,(t,s)=>{if(e.pending=!1,this[Ce]-=1,t)return this.emit("error",t);this[ps](e,s)})}[ps](e,t){this.readdirCache.set(e.absolute,t),e.readdir=t,this[Dt]()}[Dt](){if(!this[ls]){this[ls]=!0;for(let e=this[Ae].head;e!==null&&this[Ce]<this.jobs;e=e.next)if(this[$u](e.value),e.value.ignore){let t=e.next;this[Ae].removeNode(e),e.next=t}this[ls]=!1,this[cs]&&!this[Ae].length&&this[Ce]===0&&(this.zip?this.zip.end(Uu):(super.write(Uu),super.end()))}}get[$t](){return this[Ae]&&this[Ae].head&&this[Ae].head.value}[ln](e){this[Ae].shift(),this[Ce]-=1,this[Dt]()}[$u](e){if(!e.pending){if(e.entry){e===this[$t]&&!e.piped&&this[ms](e);return}if(e.stat||(this.statCache.has(e.absolute)?this[hs](e,this.statCache.get(e.absolute)):this[mn](e)),!!e.stat&&!e.ignore&&!(!this.noDirRecurse&&e.stat.isDirectory()&&!e.readdir&&(this.readdirCache.has(e.absolute)?this[ps](e,this.readdirCache.get(e.absolute)):this[dn](e),!e.readdir))){if(e.entry=this[zu](e),!e.entry){e.ignore=!0;return}e===this[$t]&&!e.piped&&this[ms](e)}}}[hn](e){return{onwarn:(t,s,i)=>this.warn(t,s,i),noPax:this.noPax,cwd:this.cwd,absolute:e.absolute,preservePaths:this.preservePaths,maxReadSize:this.maxReadSize,strict:this.strict,portable:this.portable,linkCache:this.linkCache,statCache:this.statCache,noMtime:this.noMtime,mtime:this.mtime,prefix:this.prefix}}[zu](e){this[Ce]+=1;try{return new this[Dn](e.path,this[hn](e)).on("end",()=>this[ln](e)).on("error",t=>this.emit("error",t))}catch(t){this.emit("error",t)}}[fn](){this[$t]&&this[$t].entry&&this[$t].entry.resume()}[ms](e){e.piped=!0,e.readdir&&e.readdir.forEach(i=>{let n=e.path,o=n==="./"?"":n.replace(/\/*$/,"/");this[fs](o+i)});let t=e.entry,s=this.zip;s?t.on("data",i=>{s.write(i)||t.pause()}):t.on("data",i=>{super.write(i)||t.pause()})}pause(){return this.zip&&this.zip.pause(),super.pause()}}),gn=class extends yn{constructor(e){super(e),this[Dn]=Vh}pause(){}resume(){}[mn](e){let t=this.follow?"statSync":"lstatSync";this[hs](e,Ds[t](e.absolute))}[dn](e,t){this[ps](e,Ds.readdirSync(e.absolute))}[ms](e){let t=e.entry,s=this.zip;e.readdir&&e.readdir.forEach(i=>{let n=e.path,o=n==="./"?"":n.replace(/\/*$/,"/");this[fs](o+i)}),s?t.on("data",i=>{s.write(i)}):t.on("data",i=>{super[Hu](i)})}};yn.Sync=gn;Ju.exports=yn});var Kt=E(Ar=>{"use strict";u();var Xh=bi(),Qh=require("events").EventEmitter,Y=require("fs"),wn=Y.writev;if(!wn){let r=process.binding("fs"),e=r.FSReqWrap||r.FSReqCallback;wn=(t,s,i,n)=>{let o=(l,c)=>n(l,c,s),a=new e;a.oncomplete=o,r.writeBuffers(t,s,i,a)}}var Vt=Symbol("_autoClose"),fe=Symbol("_close"),yr=Symbol("_ended"),B=Symbol("_fd"),Vu=Symbol("_finished"),Ze=Symbol("_flags"),An=Symbol("_flush"),Fn=Symbol("_handleChunk"),bn=Symbol("_makeBuf"),ws=Symbol("_mode"),Es=Symbol("_needDrain"),Ht=Symbol("_onerror"),Yt=Symbol("_onopen"),Cn=Symbol("_onread"),zt=Symbol("_onwrite"),Xe=Symbol("_open"),Ne=Symbol("_path"),gt=Symbol("_pos"),we=Symbol("_queue"),Gt=Symbol("_read"),Yu=Symbol("_readSize"),Ke=Symbol("_reading"),ys=Symbol("_remain"),Ku=Symbol("_size"),As=Symbol("_write"),Wt=Symbol("_writing"),Cs=Symbol("_defaultFlag"),Jt=Symbol("_errored"),Fs=class extends Xh{constructor(e,t){if(t=t||{},super(t),this.readable=!0,this.writable=!1,typeof e!="string")throw new TypeError("path must be a string");this[Jt]=!1,this[B]=typeof t.fd=="number"?t.fd:null,this[Ne]=e,this[Yu]=t.readSize||16*1024*1024,this[Ke]=!1,this[Ku]=typeof t.size=="number"?t.size:1/0,this[ys]=this[Ku],this[Vt]=typeof t.autoClose=="boolean"?t.autoClose:!0,typeof this[B]=="number"?this[Gt]():this[Xe]()}get fd(){return this[B]}get path(){return this[Ne]}write(){throw new TypeError("this is a readable stream")}end(){throw new TypeError("this is a readable stream")}[Xe](){Y.open(this[Ne],"r",(e,t)=>this[Yt](e,t))}[Yt](e,t){e?this[Ht](e):(this[B]=t,this.emit("open",t),this[Gt]())}[bn](){return Buffer.allocUnsafe(Math.min(this[Yu],this[ys]))}[Gt](){if(!this[Ke]){this[Ke]=!0;let e=this[bn]();if(e.length===0)return process.nextTick(()=>this[Cn](null,0,e));Y.read(this[B],e,0,e.length,null,(t,s,i)=>this[Cn](t,s,i))}}[Cn](e,t,s){this[Ke]=!1,e?this[Ht](e):this[Fn](t,s)&&this[Gt]()}[fe](){if(this[Vt]&&typeof this[B]=="number"){let e=this[B];this[B]=null,Y.close(e,t=>t?this.emit("error",t):this.emit("close"))}}[Ht](e){this[Ke]=!0,this[fe](),this.emit("error",e)}[Fn](e,t){let s=!1;return this[ys]-=e,e>0&&(s=super.write(e<t.length?t.slice(0,e):t)),(e===0||this[ys]<=0)&&(s=!1,this[fe](),super.end()),s}emit(e,t){switch(e){case"prefinish":case"finish":break;case"drain":typeof this[B]=="number"&&this[Gt]();break;case"error":return this[Jt]?void 0:(this[Jt]=!0,super.emit(e,t));default:return super.emit(e,t)}}},Sn=class extends Fs{[Xe](){let e=!0;try{this[Yt](null,Y.openSync(this[Ne],"r")),e=!1}finally{e&&this[fe]()}}[Gt](){let e=!0;try{if(!this[Ke]){this[Ke]=!0;do{let t=this[bn](),s=t.length===0?0:Y.readSync(this[B],t,0,t.length,null);if(!this[Fn](s,t))break}while(!0);this[Ke]=!1}e=!1}finally{e&&this[fe]()}}[fe](){if(this[Vt]&&typeof this[B]=="number"){let e=this[B];this[B]=null,Y.closeSync(e),this.emit("close")}}},bs=class extends Qh{constructor(e,t){t=t||{},super(t),this.readable=!1,this.writable=!0,this[Jt]=!1,this[Wt]=!1,this[yr]=!1,this[Es]=!1,this[we]=[],this[Ne]=e,this[B]=typeof t.fd=="number"?t.fd:null,this[ws]=t.mode===void 0?438:t.mode,this[gt]=typeof t.start=="number"?t.start:null,this[Vt]=typeof t.autoClose=="boolean"?t.autoClose:!0;let s=this[gt]!==null?"r+":"w";this[Cs]=t.flags===void 0,this[Ze]=this[Cs]?s:t.flags,this[B]===null&&this[Xe]()}emit(e,t){if(e==="error"){if(this[Jt])return;this[Jt]=!0}return super.emit(e,t)}get fd(){return this[B]}get path(){return this[Ne]}[Ht](e){this[fe](),this[Wt]=!0,this.emit("error",e)}[Xe](){Y.open(this[Ne],this[Ze],this[ws],(e,t)=>this[Yt](e,t))}[Yt](e,t){this[Cs]&&this[Ze]==="r+"&&e&&e.code==="ENOENT"?(this[Ze]="w",this[Xe]()):e?this[Ht](e):(this[B]=t,this.emit("open",t),this[An]())}end(e,t){return e&&this.write(e,t),this[yr]=!0,!this[Wt]&&!this[we].length&&typeof this[B]=="number"&&this[zt](null,0),this}write(e,t){return typeof e=="string"&&(e=Buffer.from(e,t)),this[yr]?(this.emit("error",new Error("write() after end()")),!1):this[B]===null||this[Wt]||this[we].length?(this[we].push(e),this[Es]=!0,!1):(this[Wt]=!0,this[As](e),!0)}[As](e){Y.write(this[B],e,0,e.length,this[gt],(t,s)=>this[zt](t,s))}[zt](e,t){e?this[Ht](e):(this[gt]!==null&&(this[gt]+=t),this[we].length?this[An]():(this[Wt]=!1,this[yr]&&!this[Vu]?(this[Vu]=!0,this[fe](),this.emit("finish")):this[Es]&&(this[Es]=!1,this.emit("drain"))))}[An](){if(this[we].length===0)this[yr]&&this[zt](null,0);else if(this[we].length===1)this[As](this[we].pop());else{let e=this[we];this[we]=[],wn(this[B],e,this[gt],(t,s)=>this[zt](t,s))}}[fe](){if(this[Vt]&&typeof this[B]=="number"){let e=this[B];this[B]=null,Y.close(e,t=>t?this.emit("error",t):this.emit("close"))}}},kn=class extends bs{[Xe](){let e;if(this[Cs]&&this[Ze]==="r+")try{e=Y.openSync(this[Ne],this[Ze],this[ws])}catch(t){if(t.code==="ENOENT")return this[Ze]="w",this[Xe]();throw t}else e=Y.openSync(this[Ne],this[Ze],this[ws]);this[Yt](null,e)}[fe](){if(this[Vt]&&typeof this[B]=="number"){let e=this[B];this[B]=null,Y.closeSync(e),this.emit("close")}}[As](e){let t=!0;try{this[zt](null,Y.writeSync(this[B],e,0,e.length,this[gt])),t=!1}finally{if(t)try{this[fe]()}catch{}}}};Ar.ReadStream=Fs;Ar.ReadStreamSync=Sn;Ar.WriteStream=bs;Ar.WriteStreamSync=kn});var xs=E((t0,ia)=>{"use strict";u();var ef=ss(),tf=qt(),rf=require("events"),sf=cn(),nf=1024*1024,of=es(),Zu=rs(),uf=qi(),{nextTick:af}=require("process"),_n=Buffer.from([31,139]),ie=Symbol("state"),Et=Symbol("writeEntry"),Le=Symbol("readEntry"),Rn=Symbol("nextEntry"),Xu=Symbol("processEntry"),ne=Symbol("extendedHeader"),Cr=Symbol("globalExtendedHeader"),Qe=Symbol("meta"),Qu=Symbol("emitMeta"),O=Symbol("buffer"),Me=Symbol("queue"),yt=Symbol("ended"),ea=Symbol("emittedEnd"),At=Symbol("emit"),K=Symbol("unzip"),Ss=Symbol("consumeChunk"),ks=Symbol("consumeChunkSub"),Bn=Symbol("consumeBody"),ta=Symbol("consumeMeta"),ra=Symbol("consumeHeader"),_s=Symbol("consuming"),vn=Symbol("bufferConcat"),xn=Symbol("maybeEnd"),wr=Symbol("writing"),et=Symbol("aborted"),Rs=Symbol("onDone"),Ct=Symbol("sawValidEntry"),Bs=Symbol("sawNullBlock"),vs=Symbol("sawEOF"),sa=Symbol("closeStream"),cf=r=>!0;ia.exports=ef(class extends rf{constructor(e){e=e||{},super(e),this.file=e.file||"",this[Ct]=null,this.on(Rs,t=>{(this[ie]==="begin"||this[Ct]===!1)&&this.warn("TAR_BAD_ARCHIVE","Unrecognized archive format")}),e.ondone?this.on(Rs,e.ondone):this.on(Rs,t=>{this.emit("prefinish"),this.emit("finish"),this.emit("end")}),this.strict=!!e.strict,this.maxMetaEntrySize=e.maxMetaEntrySize||nf,this.filter=typeof e.filter=="function"?e.filter:cf,this.writable=!0,this.readable=!1,this[Me]=new sf,this[O]=null,this[Le]=null,this[Et]=null,this[ie]="begin",this[Qe]="",this[ne]=null,this[Cr]=null,this[yt]=!1,this[K]=null,this[et]=!1,this[Bs]=!1,this[vs]=!1,this.on("end",()=>this[sa]()),typeof e.onwarn=="function"&&this.on("warn",e.onwarn),typeof e.onentry=="function"&&this.on("entry",e.onentry)}[ra](e,t){this[Ct]===null&&(this[Ct]=!1);let s;try{s=new tf(e,t,this[ne],this[Cr])}catch(i){return this.warn("TAR_ENTRY_INVALID",i)}if(s.nullBlock)this[Bs]?(this[vs]=!0,this[ie]==="begin"&&(this[ie]="header"),this[At]("eof")):(this[Bs]=!0,this[At]("nullBlock"));else if(this[Bs]=!1,!s.cksumValid)this.warn("TAR_ENTRY_INVALID","checksum failure",{header:s});else if(!s.path)this.warn("TAR_ENTRY_INVALID","path is required",{header:s});else{let i=s.type;if(/^(Symbolic)?Link$/.test(i)&&!s.linkpath)this.warn("TAR_ENTRY_INVALID","linkpath required",{header:s});else if(!/^(Symbolic)?Link$/.test(i)&&s.linkpath)this.warn("TAR_ENTRY_INVALID","linkpath forbidden",{header:s});else{let n=this[Et]=new of(s,this[ne],this[Cr]);if(!this[Ct])if(n.remain){let o=()=>{n.invalid||(this[Ct]=!0)};n.on("end",o)}else this[Ct]=!0;n.meta?n.size>this.maxMetaEntrySize?(n.ignore=!0,this[At]("ignoredEntry",n),this[ie]="ignore",n.resume()):n.size>0&&(this[Qe]="",n.on("data",o=>this[Qe]+=o),this[ie]="meta"):(this[ne]=null,n.ignore=n.ignore||!this.filter(n.path,n),n.ignore?(this[At]("ignoredEntry",n),this[ie]=n.remain?"ignore":"header",n.resume()):(n.remain?this[ie]="body":(this[ie]="header",n.end()),this[Le]?this[Me].push(n):(this[Me].push(n),this[Rn]())))}}}[sa](){af(()=>this.emit("close"))}[Xu](e){let t=!0;return e?Array.isArray(e)?this.emit.apply(this,e):(this[Le]=e,this.emit("entry",e),e.emittedEnd||(e.on("end",s=>this[Rn]()),t=!1)):(this[Le]=null,t=!1),t}[Rn](){do;while(this[Xu](this[Me].shift()));if(!this[Me].length){let e=this[Le];!e||e.flowing||e.size===e.remain?this[wr]||this.emit("drain"):e.once("drain",s=>this.emit("drain"))}}[Bn](e,t){let s=this[Et],i=s.blockRemain,n=i>=e.length&&t===0?e:e.slice(t,t+i);return s.write(n),s.blockRemain||(this[ie]="header",this[Et]=null,s.end()),n.length}[ta](e,t){let s=this[Et],i=this[Bn](e,t);return this[Et]||this[Qu](s),i}[At](e,t,s){!this[Me].length&&!this[Le]?this.emit(e,t,s):this[Me].push([e,t,s])}[Qu](e){switch(this[At]("meta",this[Qe]),e.type){case"ExtendedHeader":case"OldExtendedHeader":this[ne]=Zu.parse(this[Qe],this[ne],!1);break;case"GlobalExtendedHeader":this[Cr]=Zu.parse(this[Qe],this[Cr],!0);break;case"NextFileHasLongPath":case"OldGnuLongPath":this[ne]=this[ne]||Object.create(null),this[ne].path=this[Qe].replace(/\0.*/,"");break;case"NextFileHasLongLinkpath":this[ne]=this[ne]||Object.create(null),this[ne].linkpath=this[Qe].replace(/\0.*/,"");break;default:throw new Error("unknown meta: "+e.type)}}abort(e){this[et]=!0,this.emit("abort",e),this.warn("TAR_ABORT",e,{recoverable:!1})}write(e){if(this[et])return;if(this[K]===null&&e){if(this[O]&&(e=Buffer.concat([this[O],e]),this[O]=null),e.length<_n.length)return this[O]=e,!0;for(let s=0;this[K]===null&&s<_n.length;s++)e[s]!==_n[s]&&(this[K]=!1);if(this[K]===null){let s=this[yt];this[yt]=!1,this[K]=new uf.Unzip,this[K].on("data",n=>this[Ss](n)),this[K].on("error",n=>this.abort(n)),this[K].on("end",n=>{this[yt]=!0,this[Ss]()}),this[wr]=!0;let i=this[K][s?"end":"write"](e);return this[wr]=!1,i}}this[wr]=!0,this[K]?this[K].write(e):this[Ss](e),this[wr]=!1;let t=this[Me].length?!1:this[Le]?this[Le].flowing:!0;return!t&&!this[Me].length&&this[Le].once("drain",s=>this.emit("drain")),t}[vn](e){e&&!this[et]&&(this[O]=this[O]?Buffer.concat([this[O],e]):e)}[xn](){if(this[yt]&&!this[ea]&&!this[et]&&!this[_s]){this[ea]=!0;let e=this[Et];if(e&&e.blockRemain){let t=this[O]?this[O].length:0;this.warn("TAR_BAD_ARCHIVE",`Truncated input (needed ${e.blockRemain} more bytes, only ${t} available)`,{entry:e}),this[O]&&e.write(this[O]),e.end()}this[At](Rs)}}[Ss](e){if(this[_s])this[vn](e);else if(!e&&!this[O])this[xn]();else{if(this[_s]=!0,this[O]){this[vn](e);let t=this[O];this[O]=null,this[ks](t)}else this[ks](e);for(;this[O]&&this[O].length>=512&&!this[et]&&!this[vs];){let t=this[O];this[O]=null,this[ks](t)}this[_s]=!1}(!this[O]||this[yt])&&this[xn]()}[ks](e){let t=0,s=e.length;for(;t+512<=s&&!this[et]&&!this[vs];)switch(this[ie]){case"begin":case"header":this[ra](e,t),t+=512;break;case"ignore":case"body":t+=this[Bn](e,t);break;case"meta":t+=this[ta](e,t);break;default:throw new Error("invalid state: "+this[ie])}t<s&&(this[O]?this[O]=Buffer.concat([e.slice(t),this[O]]):this[O]=e.slice(t))}end(e){this[et]||(this[K]?this[K].end(e):(this[yt]=!0,this.write(e)))}})});var Os=E((r0,aa)=>{"use strict";u();var lf=Tt(),oa=xs(),Zt=require("fs"),hf=Kt(),na=require("path"),On=Ut();aa.exports=(r,e,t)=>{typeof r=="function"?(t=r,e=null,r={}):Array.isArray(r)&&(e=r,r={}),typeof e=="function"&&(t=e,e=null),e?e=Array.from(e):e=[];let s=lf(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return e.length&&pf(s,e),s.noResume||ff(s),s.file&&s.sync?mf(s):s.file?df(s,t):ua(s)};var ff=r=>{let e=r.onentry;r.onentry=e?t=>{e(t),t.resume()}:t=>t.resume()},pf=(r,e)=>{let t=new Map(e.map(n=>[On(n),!0])),s=r.filter,i=(n,o)=>{let a=o||na.parse(n).root||".",l=n===a?!1:t.has(n)?t.get(n):i(na.dirname(n),a);return t.set(n,l),l};r.filter=s?(n,o)=>s(n,o)&&i(On(n)):n=>i(On(n))},mf=r=>{let e=ua(r),t=r.file,s=!0,i;try{let n=Zt.statSync(t),o=r.maxReadSize||16*1024*1024;if(n.size<o)e.end(Zt.readFileSync(t));else{let a=0,l=Buffer.allocUnsafe(o);for(i=Zt.openSync(t,"r");a<n.size;){let c=Zt.readSync(i,l,0,o,a);a+=c,e.write(l.slice(0,c))}e.end()}s=!1}finally{if(s&&i)try{Zt.closeSync(i)}catch{}}},df=(r,e)=>{let t=new oa(r),s=r.maxReadSize||16*1024*1024,i=r.file,n=new Promise((o,a)=>{t.on("error",a),t.on("end",o),Zt.stat(i,(l,c)=>{if(l)a(l);else{let h=new hf.ReadStream(i,{readSize:s,size:c.size});h.on("error",a),h.pipe(t)}})});return e?n.then(e,e):n},ua=r=>new oa(r)});var ma=E((s0,pa)=>{"use strict";u();var Df=Tt(),Ts=gs(),ca=Kt(),la=Os(),ha=require("path");pa.exports=(r,e,t)=>{if(typeof e=="function"&&(t=e),Array.isArray(r)&&(e=r,r={}),!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");e=Array.from(e);let s=Df(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return s.file&&s.sync?gf(s,e):s.file?Ef(s,e,t):s.sync?yf(s,e):Af(s,e)};var gf=(r,e)=>{let t=new Ts.Sync(r),s=new ca.WriteStreamSync(r.file,{mode:r.mode||438});t.pipe(s),fa(t,e)},Ef=(r,e,t)=>{let s=new Ts(r),i=new ca.WriteStream(r.file,{mode:r.mode||438});s.pipe(i);let n=new Promise((o,a)=>{i.on("error",a),i.on("close",o),s.on("error",a)});return Tn(s,e),t?n.then(t,t):n},fa=(r,e)=>{e.forEach(t=>{t.charAt(0)==="@"?la({file:ha.resolve(r.cwd,t.slice(1)),sync:!0,noResume:!0,onentry:s=>r.add(s)}):r.add(t)}),r.end()},Tn=(r,e)=>{for(;e.length;){let t=e.shift();if(t.charAt(0)==="@")return la({file:ha.resolve(r.cwd,t.slice(1)),noResume:!0,onentry:s=>r.add(s)}).then(s=>Tn(r,e));r.add(t)}r.end()},yf=(r,e)=>{let t=new Ts.Sync(r);return fa(t,e),t},Af=(r,e)=>{let t=new Ts(r);return Tn(t,e),t}});var Pn=E((i0,Ca)=>{"use strict";u();var Cf=Tt(),da=gs(),Q=require("fs"),Da=Kt(),ga=Os(),Ea=require("path"),ya=qt();Ca.exports=(r,e,t)=>{let s=Cf(r);if(!s.file)throw new TypeError("file is required");if(s.gzip)throw new TypeError("cannot append to compressed archives");if(!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");return e=Array.from(e),s.sync?wf(s,e):bf(s,e,t)};var wf=(r,e)=>{let t=new da.Sync(r),s=!0,i,n;try{try{i=Q.openSync(r.file,"r+")}catch(l){if(l.code==="ENOENT")i=Q.openSync(r.file,"w+");else throw l}let o=Q.fstatSync(i),a=Buffer.alloc(512);e:for(n=0;n<o.size;n+=512){for(let h=0,m=0;h<512;h+=m){if(m=Q.readSync(i,a,h,a.length-h,n+h),n===0&&a[0]===31&&a[1]===139)throw new Error("cannot append to compressed archives");if(!m)break e}let l=new ya(a);if(!l.cksumValid)break;let c=512*Math.ceil(l.size/512);if(n+c+512>o.size)break;n+=c,r.mtimeCache&&r.mtimeCache.set(l.path,l.mtime)}s=!1,Ff(r,t,n,i,e)}finally{if(s)try{Q.closeSync(i)}catch{}}},Ff=(r,e,t,s,i)=>{let n=new Da.WriteStreamSync(r.file,{fd:s,start:t});e.pipe(n),Sf(e,i)},bf=(r,e,t)=>{e=Array.from(e);let s=new da(r),i=(o,a,l)=>{let c=(b,T)=>{b?Q.close(o,S=>l(b)):l(null,T)},h=0;if(a===0)return c(null,0);let m=0,D=Buffer.alloc(512),A=(b,T)=>{if(b)return c(b);if(m+=T,m<512&&T)return Q.read(o,D,m,D.length-m,h+m,A);if(h===0&&D[0]===31&&D[1]===139)return c(new Error("cannot append to compressed archives"));if(m<512)return c(null,h);let S=new ya(D);if(!S.cksumValid)return c(null,h);let x=512*Math.ceil(S.size/512);if(h+x+512>a||(h+=x+512,h>=a))return c(null,h);r.mtimeCache&&r.mtimeCache.set(S.path,S.mtime),m=0,Q.read(o,D,0,512,h,A)};Q.read(o,D,0,512,h,A)},n=new Promise((o,a)=>{s.on("error",a);let l="r+",c=(h,m)=>{if(h&&h.code==="ENOENT"&&l==="r+")return l="w+",Q.open(r.file,l,c);if(h)return a(h);Q.fstat(m,(D,A)=>{if(D)return Q.close(m,()=>a(D));i(m,A.size,(b,T)=>{if(b)return a(b);let S=new Da.WriteStream(r.file,{fd:m,start:T});s.pipe(S),S.on("error",a),S.on("close",o),Aa(s,e)})})};Q.open(r.file,l,c)});return t?n.then(t,t):n},Sf=(r,e)=>{e.forEach(t=>{t.charAt(0)==="@"?ga({file:Ea.resolve(r.cwd,t.slice(1)),sync:!0,noResume:!0,onentry:s=>r.add(s)}):r.add(t)}),r.end()},Aa=(r,e)=>{for(;e.length;){let t=e.shift();if(t.charAt(0)==="@")return ga({file:Ea.resolve(r.cwd,t.slice(1)),noResume:!0,onentry:s=>r.add(s)}).then(s=>Aa(r,e));r.add(t)}r.end()}});var Fa=E((n0,wa)=>{"use strict";u();var kf=Tt(),_f=Pn();wa.exports=(r,e,t)=>{let s=kf(r);if(!s.file)throw new TypeError("file is required");if(s.gzip)throw new TypeError("cannot append to compressed archives");if(!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");return e=Array.from(e),Rf(s),_f(s,e,t)};var Rf=r=>{let e=r.filter;r.mtimeCache||(r.mtimeCache=new Map),r.filter=e?(t,s)=>e(t,s)&&!(r.mtimeCache.get(t)>s.mtime):(t,s)=>!(r.mtimeCache.get(t)>s.mtime)}});var ka=E((o0,Sa)=>{u();var{promisify:ba}=require("util"),tt=require("fs"),Bf=r=>{if(!r)r={mode:511,fs:tt};else if(typeof r=="object")r={mode:511,fs:tt,...r};else if(typeof r=="number")r={mode:r,fs:tt};else if(typeof r=="string")r={mode:parseInt(r,8),fs:tt};else throw new TypeError("invalid options argument");return r.mkdir=r.mkdir||r.fs.mkdir||tt.mkdir,r.mkdirAsync=ba(r.mkdir),r.stat=r.stat||r.fs.stat||tt.stat,r.statAsync=ba(r.stat),r.statSync=r.statSync||r.fs.statSync||tt.statSync,r.mkdirSync=r.mkdirSync||r.fs.mkdirSync||tt.mkdirSync,r};Sa.exports=Bf});var Ra=E((u0,_a)=>{u();var vf=process.env.__TESTING_MKDIRP_PLATFORM__||process.platform,{resolve:xf,parse:Of}=require("path"),Tf=r=>{if(/\0/.test(r))throw Object.assign(new TypeError("path must be a string without null bytes"),{path:r,code:"ERR_INVALID_ARG_VALUE"});if(r=xf(r),vf==="win32"){let e=/[*|"<>?:]/,{root:t}=Of(r);if(e.test(r.substr(t.length)))throw Object.assign(new Error("Illegal characters in path."),{path:r,code:"EINVAL"})}return r};_a.exports=Tf});var Ta=E((a0,Oa)=>{u();var{dirname:Ba}=require("path"),va=(r,e,t=void 0)=>t===e?Promise.resolve():r.statAsync(e).then(s=>s.isDirectory()?t:void 0,s=>s.code==="ENOENT"?va(r,Ba(e),e):void 0),xa=(r,e,t=void 0)=>{if(t!==e)try{return r.statSync(e).isDirectory()?t:void 0}catch(s){return s.code==="ENOENT"?xa(r,Ba(e),e):void 0}};Oa.exports={findMade:va,findMadeSync:xa}});var Mn=E((c0,Na)=>{u();var{dirname:Pa}=require("path"),Nn=(r,e,t)=>{e.recursive=!1;let s=Pa(r);return s===r?e.mkdirAsync(r,e).catch(i=>{if(i.code!=="EISDIR")throw i}):e.mkdirAsync(r,e).then(()=>t||r,i=>{if(i.code==="ENOENT")return Nn(s,e).then(n=>Nn(r,e,n));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;return e.statAsync(r).then(n=>{if(n.isDirectory())return t;throw i},()=>{throw i})})},Ln=(r,e,t)=>{let s=Pa(r);if(e.recursive=!1,s===r)try{return e.mkdirSync(r,e)}catch(i){if(i.code!=="EISDIR")throw i;return}try{return e.mkdirSync(r,e),t||r}catch(i){if(i.code==="ENOENT")return Ln(r,e,Ln(s,e,t));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;try{if(!e.statSync(r).isDirectory())throw i}catch{throw i}}};Na.exports={mkdirpManual:Nn,mkdirpManualSync:Ln}});var Ia=E((l0,Ma)=>{u();var{dirname:La}=require("path"),{findMade:Pf,findMadeSync:Nf}=Ta(),{mkdirpManual:Lf,mkdirpManualSync:Mf}=Mn(),If=(r,e)=>(e.recursive=!0,La(r)===r?e.mkdirAsync(r,e):Pf(e,r).then(s=>e.mkdirAsync(r,e).then(()=>s).catch(i=>{if(i.code==="ENOENT")return Lf(r,e);throw i}))),jf=(r,e)=>{if(e.recursive=!0,La(r)===r)return e.mkdirSync(r,e);let s=Nf(e,r);try{return e.mkdirSync(r,e),s}catch(i){if(i.code==="ENOENT")return Mf(r,e);throw i}};Ma.exports={mkdirpNative:If,mkdirpNativeSync:jf}});var $a=E((h0,Ua)=>{u();var ja=require("fs"),qf=process.env.__TESTING_MKDIRP_NODE_VERSION__||process.version,In=qf.replace(/^v/,"").split("."),qa=+In[0]>10||+In[0]==10&&+In[1]>=12,Uf=qa?r=>r.mkdir===ja.mkdir:()=>!1,$f=qa?r=>r.mkdirSync===ja.mkdirSync:()=>!1;Ua.exports={useNative:Uf,useNativeSync:$f}});var Va=E((f0,Ja)=>{u();var Xt=ka(),Qt=Ra(),{mkdirpNative:Wa,mkdirpNativeSync:za}=Ia(),{mkdirpManual:Ga,mkdirpManualSync:Ha}=Mn(),{useNative:Wf,useNativeSync:zf}=$a(),er=(r,e)=>(r=Qt(r),e=Xt(e),Wf(e)?Wa(r,e):Ga(r,e)),Gf=(r,e)=>(r=Qt(r),e=Xt(e),zf(e)?za(r,e):Ha(r,e));er.sync=Gf;er.native=(r,e)=>Wa(Qt(r),Xt(e));er.manual=(r,e)=>Ga(Qt(r),Xt(e));er.nativeSync=(r,e)=>za(Qt(r),Xt(e));er.manualSync=(r,e)=>Ha(Qt(r),Xt(e));Ja.exports=er});var tc=E((p0,ec)=>{"use strict";u();var oe=require("fs"),wt=require("path"),Hf=oe.lchown?"lchown":"chown",Jf=oe.lchownSync?"lchownSync":"chownSync",Ka=oe.lchown&&!process.version.match(/v1[1-9]+\./)&&!process.version.match(/v10\.[6-9]/),Ya=(r,e,t)=>{try{return oe[Jf](r,e,t)}catch(s){if(s.code!=="ENOENT")throw s}},Vf=(r,e,t)=>{try{return oe.chownSync(r,e,t)}catch(s){if(s.code!=="ENOENT")throw s}},Yf=Ka?(r,e,t,s)=>i=>{!i||i.code!=="EISDIR"?s(i):oe.chown(r,e,t,s)}:(r,e,t,s)=>s,jn=Ka?(r,e,t)=>{try{return Ya(r,e,t)}catch(s){if(s.code!=="EISDIR")throw s;Vf(r,e,t)}}:(r,e,t)=>Ya(r,e,t),Kf=process.version,Za=(r,e,t)=>oe.readdir(r,e,t),Zf=(r,e)=>oe.readdirSync(r,e);/^v4\./.test(Kf)&&(Za=(r,e,t)=>oe.readdir(r,t));var Ps=(r,e,t,s)=>{oe[Hf](r,e,t,Yf(r,e,t,i=>{s(i&&i.code!=="ENOENT"?i:null)}))},Xa=(r,e,t,s,i)=>{if(typeof e=="string")return oe.lstat(wt.resolve(r,e),(n,o)=>{if(n)return i(n.code!=="ENOENT"?n:null);o.name=e,Xa(r,o,t,s,i)});if(e.isDirectory())qn(wt.resolve(r,e.name),t,s,n=>{if(n)return i(n);let o=wt.resolve(r,e.name);Ps(o,t,s,i)});else{let n=wt.resolve(r,e.name);Ps(n,t,s,i)}},qn=(r,e,t,s)=>{Za(r,{withFileTypes:!0},(i,n)=>{if(i){if(i.code==="ENOENT")return s();if(i.code!=="ENOTDIR"&&i.code!=="ENOTSUP")return s(i)}if(i||!n.length)return Ps(r,e,t,s);let o=n.length,a=null,l=c=>{if(!a){if(c)return s(a=c);if(--o===0)return Ps(r,e,t,s)}};n.forEach(c=>Xa(r,c,e,t,l))})},Xf=(r,e,t,s)=>{if(typeof e=="string")try{let i=oe.lstatSync(wt.resolve(r,e));i.name=e,e=i}catch(i){if(i.code==="ENOENT")return;throw i}e.isDirectory()&&Qa(wt.resolve(r,e.name),t,s),jn(wt.resolve(r,e.name),t,s)},Qa=(r,e,t)=>{let s;try{s=Zf(r,{withFileTypes:!0})}catch(i){if(i.code==="ENOENT")return;if(i.code==="ENOTDIR"||i.code==="ENOTSUP")return jn(r,e,t);throw i}return s&&s.length&&s.forEach(i=>Xf(r,i,e,t)),jn(r,e,t)};ec.exports=qn;qn.sync=Qa});var nc=E((m0,Un)=>{"use strict";u();var rc=Va(),ue=require("fs"),Ns=require("path"),sc=tc(),pe=It(),Ls=class extends Error{constructor(e,t){super("Cannot extract through symbolic link"),this.path=t,this.symlink=e}get name(){return"SylinkError"}},Ms=class extends Error{constructor(e,t){super(t+": Cannot cd into '"+e+"'"),this.path=e,this.code=t}get name(){return"CwdError"}},Is=(r,e)=>r.get(pe(e)),Fr=(r,e,t)=>r.set(pe(e),t),Qf=(r,e)=>{ue.stat(r,(t,s)=>{(t||!s.isDirectory())&&(t=new Ms(r,t&&t.code||"ENOTDIR")),e(t)})};Un.exports=(r,e,t)=>{r=pe(r);let s=e.umask,i=e.mode|448,n=(i&s)!==0,o=e.uid,a=e.gid,l=typeof o=="number"&&typeof a=="number"&&(o!==e.processUid||a!==e.processGid),c=e.preserve,h=e.unlink,m=e.cache,D=pe(e.cwd),A=(S,x)=>{S?t(S):(Fr(m,r,!0),x&&l?sc(x,o,a,ut=>A(ut)):n?ue.chmod(r,i,t):t())};if(m&&Is(m,r)===!0)return A();if(r===D)return Qf(r,A);if(c)return rc(r,{mode:i}).then(S=>A(null,S),A);let T=pe(Ns.relative(D,r)).split("/");js(D,T,i,m,h,D,null,A)};var js=(r,e,t,s,i,n,o,a)=>{if(!e.length)return a(null,o);let l=e.shift(),c=pe(Ns.resolve(r+"/"+l));if(Is(s,c))return js(c,e,t,s,i,n,o,a);ue.mkdir(c,t,ic(c,e,t,s,i,n,o,a))},ic=(r,e,t,s,i,n,o,a)=>l=>{l?ue.lstat(r,(c,h)=>{if(c)c.path=c.path&&pe(c.path),a(c);else if(h.isDirectory())js(r,e,t,s,i,n,o,a);else if(i)ue.unlink(r,m=>{if(m)return a(m);ue.mkdir(r,t,ic(r,e,t,s,i,n,o,a))});else{if(h.isSymbolicLink())return a(new Ls(r,r+"/"+e.join("/")));a(l)}}):(o=o||r,js(r,e,t,s,i,n,o,a))},ep=r=>{let e=!1,t="ENOTDIR";try{e=ue.statSync(r).isDirectory()}catch(s){t=s.code}finally{if(!e)throw new Ms(r,t)}};Un.exports.sync=(r,e)=>{r=pe(r);let t=e.umask,s=e.mode|448,i=(s&t)!==0,n=e.uid,o=e.gid,a=typeof n=="number"&&typeof o=="number"&&(n!==e.processUid||o!==e.processGid),l=e.preserve,c=e.unlink,h=e.cache,m=pe(e.cwd),D=S=>{Fr(h,r,!0),S&&a&&sc.sync(S,n,o),i&&ue.chmodSync(r,s)};if(h&&Is(h,r)===!0)return D();if(r===m)return ep(m),D();if(l)return D(rc.sync(r,s));let b=pe(Ns.relative(m,r)).split("/"),T=null;for(let S=b.shift(),x=m;S&&(x+="/"+S);S=b.shift())if(x=pe(Ns.resolve(x)),!Is(h,x))try{ue.mkdirSync(x,s),T=T||x,Fr(h,x,!0)}catch{let de=ue.lstatSync(x);if(de.isDirectory()){Fr(h,x,!0);continue}else if(c){ue.unlinkSync(x),ue.mkdirSync(x,s),T=T||x,Fr(h,x,!0);continue}else if(de.isSymbolicLink())return new Ls(x,x+"/"+b.join("/"))}return D(T)}});var Wn=E((d0,oc)=>{u();var $n=Object.create(null),{hasOwnProperty:tp}=Object.prototype;oc.exports=r=>(tp.call($n,r)||($n[r]=r.normalize("NFKD")),$n[r])});var lc=E((D0,cc)=>{u();var uc=require("assert"),rp=Wn(),sp=Ut(),{join:ac}=require("path"),ip=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform,np=ip==="win32";cc.exports=()=>{let r=new Map,e=new Map,t=c=>c.split("/").slice(0,-1).reduce((m,D)=>(m.length&&(D=ac(m[m.length-1],D)),m.push(D||"/"),m),[]),s=new Set,i=c=>{let h=e.get(c);if(!h)throw new Error("function does not have any path reservations");return{paths:h.paths.map(m=>r.get(m)),dirs:[...h.dirs].map(m=>r.get(m))}},n=c=>{let{paths:h,dirs:m}=i(c);return h.every(D=>D[0]===c)&&m.every(D=>D[0]instanceof Set&&D[0].has(c))},o=c=>s.has(c)||!n(c)?!1:(s.add(c),c(()=>a(c)),!0),a=c=>{if(!s.has(c))return!1;let{paths:h,dirs:m}=e.get(c),D=new Set;return h.forEach(A=>{let b=r.get(A);uc.equal(b[0],c),b.length===1?r.delete(A):(b.shift(),typeof b[0]=="function"?D.add(b[0]):b[0].forEach(T=>D.add(T)))}),m.forEach(A=>{let b=r.get(A);uc(b[0]instanceof Set),b[0].size===1&&b.length===1?r.delete(A):b[0].size===1?(b.shift(),D.add(b[0])):b[0].delete(c)}),s.delete(c),D.forEach(A=>o(A)),!0};return{check:n,reserve:(c,h)=>{c=np?["win32 parallelization disabled"]:c.map(D=>rp(sp(ac(D))).toLowerCase());let m=new Set(c.map(D=>t(D)).reduce((D,A)=>D.concat(A)));return e.set(h,{dirs:m,paths:c}),c.forEach(D=>{let A=r.get(D);A?A.push(h):r.set(D,[h])}),m.forEach(D=>{let A=r.get(D);A?A[A.length-1]instanceof Set?A[A.length-1].add(h):A.push(new Set([h])):r.set(D,[new Set([h])])}),o(h)}}}});var pc=E((g0,fc)=>{u();var op=process.env.__FAKE_PLATFORM__||process.platform,up=op==="win32",ap=global.__FAKE_TESTING_FS__||require("fs"),{O_CREAT:cp,O_TRUNC:lp,O_WRONLY:hp,UV_FS_O_FILEMAP:hc=0}=ap.constants,fp=up&&!!hc,pp=512*1024,mp=hc|lp|cp|hp;fc.exports=fp?r=>r<pp?mp:"w":()=>"w"});var Xn=E((E0,_c)=>{"use strict";u();var dp=require("assert"),Dp=xs(),_=require("fs"),gp=Kt(),Ie=require("path"),bc=nc(),mc=Ki(),Ep=lc(),yp=Zi(),ee=It(),Ap=Ut(),Cp=Wn(),dc=Symbol("onEntry"),Hn=Symbol("checkFs"),Dc=Symbol("checkFs2"),$s=Symbol("pruneCache"),Jn=Symbol("isReusable"),ae=Symbol("makeFs"),Vn=Symbol("file"),Yn=Symbol("directory"),Ws=Symbol("link"),gc=Symbol("symlink"),Ec=Symbol("hardlink"),yc=Symbol("unsupported"),Ac=Symbol("checkPath"),rt=Symbol("mkdir"),z=Symbol("onError"),qs=Symbol("pending"),Cc=Symbol("pend"),tr=Symbol("unpend"),zn=Symbol("ended"),Gn=Symbol("maybeClose"),Kn=Symbol("skip"),br=Symbol("doChown"),Sr=Symbol("uid"),kr=Symbol("gid"),_r=Symbol("checkedCwd"),Sc=require("crypto"),kc=pc(),wp=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform,Rr=wp==="win32",Fp=(r,e)=>{if(!Rr)return _.unlink(r,e);let t=r+".DELETE."+Sc.randomBytes(16).toString("hex");_.rename(r,t,s=>{if(s)return e(s);_.unlink(t,e)})},bp=r=>{if(!Rr)return _.unlinkSync(r);let e=r+".DELETE."+Sc.randomBytes(16).toString("hex");_.renameSync(r,e),_.unlinkSync(e)},wc=(r,e,t)=>r===r>>>0?r:e===e>>>0?e:t,Fc=r=>Cp(Ap(ee(r))).toLowerCase(),Sp=(r,e)=>{e=Fc(e);for(let t of r.keys()){let s=Fc(t);(s===e||s.indexOf(e+"/")===0)&&r.delete(t)}},kp=r=>{for(let e of r.keys())r.delete(e)},Br=class extends Dp{constructor(e){if(e||(e={}),e.ondone=t=>{this[zn]=!0,this[Gn]()},super(e),this[_r]=!1,this.reservations=Ep(),this.transform=typeof e.transform=="function"?e.transform:null,this.writable=!0,this.readable=!1,this[qs]=0,this[zn]=!1,this.dirCache=e.dirCache||new Map,typeof e.uid=="number"||typeof e.gid=="number"){if(typeof e.uid!="number"||typeof e.gid!="number")throw new TypeError("cannot set owner without number uid and gid");if(e.preserveOwner)throw new TypeError("cannot preserve owner in archive and also set owner explicitly");this.uid=e.uid,this.gid=e.gid,this.setOwner=!0}else this.uid=null,this.gid=null,this.setOwner=!1;e.preserveOwner===void 0&&typeof e.uid!="number"?this.preserveOwner=process.getuid&&process.getuid()===0:this.preserveOwner=!!e.preserveOwner,this.processUid=(this.preserveOwner||this.setOwner)&&process.getuid?process.getuid():null,this.processGid=(this.preserveOwner||this.setOwner)&&process.getgid?process.getgid():null,this.forceChown=e.forceChown===!0,this.win32=!!e.win32||Rr,this.newer=!!e.newer,this.keep=!!e.keep,this.noMtime=!!e.noMtime,this.preservePaths=!!e.preservePaths,this.unlink=!!e.unlink,this.cwd=ee(Ie.resolve(e.cwd||process.cwd())),this.strip=+e.strip||0,this.processUmask=e.noChmod?0:process.umask(),this.umask=typeof e.umask=="number"?e.umask:this.processUmask,this.dmode=e.dmode||511&~this.umask,this.fmode=e.fmode||438&~this.umask,this.on("entry",t=>this[dc](t))}warn(e,t,s={}){return(e==="TAR_BAD_ARCHIVE"||e==="TAR_ABORT")&&(s.recoverable=!1),super.warn(e,t,s)}[Gn](){this[zn]&&this[qs]===0&&(this.emit("prefinish"),this.emit("finish"),this.emit("end"))}[Ac](e){if(this.strip){let t=ee(e.path).split("/");if(t.length<this.strip)return!1;if(e.path=t.slice(this.strip).join("/"),e.type==="Link"){let s=ee(e.linkpath).split("/");if(s.length>=this.strip)e.linkpath=s.slice(this.strip).join("/");else return!1}}if(!this.preservePaths){let t=ee(e.path),s=t.split("/");if(s.includes("..")||Rr&&/^[a-z]:\.\.$/i.test(s[0]))return this.warn("TAR_ENTRY_ERROR","path contains '..'",{entry:e,path:t}),!1;let[i,n]=yp(t);i&&(e.path=n,this.warn("TAR_ENTRY_INFO",`stripping ${i} from absolute path`,{entry:e,path:t}))}if(Ie.isAbsolute(e.path)?e.absolute=ee(Ie.resolve(e.path)):e.absolute=ee(Ie.resolve(this.cwd,e.path)),!this.preservePaths&&e.absolute.indexOf(this.cwd+"/")!==0&&e.absolute!==this.cwd)return this.warn("TAR_ENTRY_ERROR","path escaped extraction target",{entry:e,path:ee(e.path),resolvedPath:e.absolute,cwd:this.cwd}),!1;if(e.absolute===this.cwd&&e.type!=="Directory"&&e.type!=="GNUDumpDir")return!1;if(this.win32){let{root:t}=Ie.win32.parse(e.absolute);e.absolute=t+mc.encode(e.absolute.slice(t.length));let{root:s}=Ie.win32.parse(e.path);e.path=s+mc.encode(e.path.slice(s.length))}return!0}[dc](e){if(!this[Ac](e))return e.resume();switch(dp.equal(typeof e.absolute,"string"),e.type){case"Directory":case"GNUDumpDir":e.mode&&(e.mode=e.mode|448);case"File":case"OldFile":case"ContiguousFile":case"Link":case"SymbolicLink":return this[Hn](e);case"CharacterDevice":case"BlockDevice":case"FIFO":default:return this[yc](e)}}[z](e,t){e.name==="CwdError"?this.emit("error",e):(this.warn("TAR_ENTRY_ERROR",e,{entry:t}),this[tr](),t.resume())}[rt](e,t,s){bc(ee(e),{uid:this.uid,gid:this.gid,processUid:this.processUid,processGid:this.processGid,umask:this.processUmask,preserve:this.preservePaths,unlink:this.unlink,cache:this.dirCache,cwd:this.cwd,mode:t,noChmod:this.noChmod},s)}[br](e){return this.forceChown||this.preserveOwner&&(typeof e.uid=="number"&&e.uid!==this.processUid||typeof e.gid=="number"&&e.gid!==this.processGid)||typeof this.uid=="number"&&this.uid!==this.processUid||typeof this.gid=="number"&&this.gid!==this.processGid}[Sr](e){return wc(this.uid,e.uid,this.processUid)}[kr](e){return wc(this.gid,e.gid,this.processGid)}[Vn](e,t){let s=e.mode&4095||this.fmode,i=new gp.WriteStream(e.absolute,{flags:kc(e.size),mode:s,autoClose:!1});i.on("error",l=>{i.fd&&_.close(i.fd,()=>{}),i.write=()=>!0,this[z](l,e),t()});let n=1,o=l=>{if(l){i.fd&&_.close(i.fd,()=>{}),this[z](l,e),t();return}--n===0&&_.close(i.fd,c=>{c?this[z](c,e):this[tr](),t()})};i.on("finish",l=>{let c=e.absolute,h=i.fd;if(e.mtime&&!this.noMtime){n++;let m=e.atime||new Date,D=e.mtime;_.futimes(h,m,D,A=>A?_.utimes(c,m,D,b=>o(b&&A)):o())}if(this[br](e)){n++;let m=this[Sr](e),D=this[kr](e);_.fchown(h,m,D,A=>A?_.chown(c,m,D,b=>o(b&&A)):o())}o()});let a=this.transform&&this.transform(e)||e;a!==e&&(a.on("error",l=>{this[z](l,e),t()}),e.pipe(a)),a.pipe(i)}[Yn](e,t){let s=e.mode&4095||this.dmode;this[rt](e.absolute,s,i=>{if(i){this[z](i,e),t();return}let n=1,o=a=>{--n===0&&(t(),this[tr](),e.resume())};e.mtime&&!this.noMtime&&(n++,_.utimes(e.absolute,e.atime||new Date,e.mtime,o)),this[br](e)&&(n++,_.chown(e.absolute,this[Sr](e),this[kr](e),o)),o()})}[yc](e){e.unsupported=!0,this.warn("TAR_ENTRY_UNSUPPORTED",`unsupported entry type: ${e.type}`,{entry:e}),e.resume()}[gc](e,t){this[Ws](e,e.linkpath,"symlink",t)}[Ec](e,t){let s=ee(Ie.resolve(this.cwd,e.linkpath));this[Ws](e,s,"link",t)}[Cc](){this[qs]++}[tr](){this[qs]--,this[Gn]()}[Kn](e){this[tr](),e.resume()}[Jn](e,t){return e.type==="File"&&!this.unlink&&t.isFile()&&t.nlink<=1&&!Rr}[Hn](e){this[Cc]();let t=[e.path];e.linkpath&&t.push(e.linkpath),this.reservations.reserve(t,s=>this[Dc](e,s))}[$s](e){e.type==="SymbolicLink"?kp(this.dirCache):e.type!=="Directory"&&Sp(this.dirCache,e.absolute)}[Dc](e,t){this[$s](e);let s=a=>{this[$s](e),t(a)},i=()=>{this[rt](this.cwd,this.dmode,a=>{if(a){this[z](a,e),s();return}this[_r]=!0,n()})},n=()=>{if(e.absolute!==this.cwd){let a=ee(Ie.dirname(e.absolute));if(a!==this.cwd)return this[rt](a,this.dmode,l=>{if(l){this[z](l,e),s();return}o()})}o()},o=()=>{_.lstat(e.absolute,(a,l)=>{if(l&&(this.keep||this.newer&&l.mtime>e.mtime)){this[Kn](e),s();return}if(a||this[Jn](e,l))return this[ae](null,e,s);if(l.isDirectory()){if(e.type==="Directory"){let c=!this.noChmod&&e.mode&&(l.mode&4095)!==e.mode,h=m=>this[ae](m,e,s);return c?_.chmod(e.absolute,e.mode,h):h()}if(e.absolute!==this.cwd)return _.rmdir(e.absolute,c=>this[ae](c,e,s))}if(e.absolute===this.cwd)return this[ae](null,e,s);Fp(e.absolute,c=>this[ae](c,e,s))})};this[_r]?n():i()}[ae](e,t,s){if(e){this[z](e,t),s();return}switch(t.type){case"File":case"OldFile":case"ContiguousFile":return this[Vn](t,s);case"Link":return this[Ec](t,s);case"SymbolicLink":return this[gc](t,s);case"Directory":case"GNUDumpDir":return this[Yn](t,s)}}[Ws](e,t,s,i){_[s](t,e.absolute,n=>{n?this[z](n,e):(this[tr](),e.resume()),i()})}},Us=r=>{try{return[null,r()]}catch(e){return[e,null]}},Zn=class extends Br{[ae](e,t){return super[ae](e,t,()=>{})}[Hn](e){if(this[$s](e),!this[_r]){let n=this[rt](this.cwd,this.dmode);if(n)return this[z](n,e);this[_r]=!0}if(e.absolute!==this.cwd){let n=ee(Ie.dirname(e.absolute));if(n!==this.cwd){let o=this[rt](n,this.dmode);if(o)return this[z](o,e)}}let[t,s]=Us(()=>_.lstatSync(e.absolute));if(s&&(this.keep||this.newer&&s.mtime>e.mtime))return this[Kn](e);if(t||this[Jn](e,s))return this[ae](null,e);if(s.isDirectory()){if(e.type==="Directory"){let o=!this.noChmod&&e.mode&&(s.mode&4095)!==e.mode,[a]=o?Us(()=>{_.chmodSync(e.absolute,e.mode)}):[];return this[ae](a,e)}let[n]=Us(()=>_.rmdirSync(e.absolute));this[ae](n,e)}let[i]=e.absolute===this.cwd?[]:Us(()=>bp(e.absolute));this[ae](i,e)}[Vn](e,t){let s=e.mode&4095||this.fmode,i=a=>{let l;try{_.closeSync(n)}catch(c){l=c}(a||l)&&this[z](a||l,e),t()},n;try{n=_.openSync(e.absolute,kc(e.size),s)}catch(a){return i(a)}let o=this.transform&&this.transform(e)||e;o!==e&&(o.on("error",a=>this[z](a,e)),e.pipe(o)),o.on("data",a=>{try{_.writeSync(n,a,0,a.length)}catch(l){i(l)}}),o.on("end",a=>{let l=null;if(e.mtime&&!this.noMtime){let c=e.atime||new Date,h=e.mtime;try{_.futimesSync(n,c,h)}catch(m){try{_.utimesSync(e.absolute,c,h)}catch{l=m}}}if(this[br](e)){let c=this[Sr](e),h=this[kr](e);try{_.fchownSync(n,c,h)}catch(m){try{_.chownSync(e.absolute,c,h)}catch{l=l||m}}}i(l)})}[Yn](e,t){let s=e.mode&4095||this.dmode,i=this[rt](e.absolute,s);if(i){this[z](i,e),t();return}if(e.mtime&&!this.noMtime)try{_.utimesSync(e.absolute,e.atime||new Date,e.mtime)}catch{}if(this[br](e))try{_.chownSync(e.absolute,this[Sr](e),this[kr](e))}catch{}t(),e.resume()}[rt](e,t){try{return bc.sync(ee(e),{uid:this.uid,gid:this.gid,processUid:this.processUid,processGid:this.processGid,umask:this.processUmask,preserve:this.preservePaths,unlink:this.unlink,cache:this.dirCache,cwd:this.cwd,mode:t})}catch(s){return s}}[Ws](e,t,s,i){try{_[s+"Sync"](t,e.absolute),i(),e.resume()}catch(n){return this[z](n,e)}}};Br.Sync=Zn;_c.exports=Br});var Oc=E((y0,xc)=>{"use strict";u();var _p=Tt(),zs=Xn(),Bc=require("fs"),vc=Kt(),Rc=require("path"),Qn=Ut();xc.exports=(r,e,t)=>{typeof r=="function"?(t=r,e=null,r={}):Array.isArray(r)&&(e=r,r={}),typeof e=="function"&&(t=e,e=null),e?e=Array.from(e):e=[];let s=_p(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return e.length&&Rp(s,e),s.file&&s.sync?Bp(s):s.file?vp(s,t):s.sync?xp(s):Op(s)};var Rp=(r,e)=>{let t=new Map(e.map(n=>[Qn(n),!0])),s=r.filter,i=(n,o)=>{let a=o||Rc.parse(n).root||".",l=n===a?!1:t.has(n)?t.get(n):i(Rc.dirname(n),a);return t.set(n,l),l};r.filter=s?(n,o)=>s(n,o)&&i(Qn(n)):n=>i(Qn(n))},Bp=r=>{let e=new zs.Sync(r),t=r.file,s=Bc.statSync(t),i=r.maxReadSize||16*1024*1024;new vc.ReadStreamSync(t,{readSize:i,size:s.size}).pipe(e)},vp=(r,e)=>{let t=new zs(r),s=r.maxReadSize||16*1024*1024,i=r.file,n=new Promise((o,a)=>{t.on("error",a),t.on("close",o),Bc.stat(i,(l,c)=>{if(l)a(l);else{let h=new vc.ReadStream(i,{readSize:s,size:c.size});h.on("error",a),h.pipe(t)}})});return e?n.then(e,e):n},xp=r=>new zs.Sync(r),Op=r=>new zs(r)});var Tc=E(L=>{"use strict";u();L.c=L.create=ma();L.r=L.replace=Pn();L.t=L.list=Os();L.u=L.update=Fa();L.x=L.extract=Oc();L.Pack=gs();L.Unpack=Xn();L.Parse=xs();L.ReadEntry=es();L.WriteEntry=an();L.Header=qt();L.Pax=rs();L.types=Wi()});var Ic=E((b0,Mc)=>{u();function ce(r,e){typeof e=="boolean"&&(e={forever:e}),this._originalTimeouts=JSON.parse(JSON.stringify(r)),this._timeouts=r,this._options=e||{},this._maxRetryTime=e&&e.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}Mc.exports=ce;ce.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)};ce.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null};ce.prototype.retry=function(r){if(this._timeout&&clearTimeout(this._timeout),!r)return!1;var e=new Date().getTime();if(r&&e-this._operationStart>=this._maxRetryTime)return this._errors.push(r),this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(r);var t=this._timeouts.shift();if(t===void 0)if(this._cachedTimeouts)this._errors.splice(0,this._errors.length-1),t=this._cachedTimeouts.slice(-1);else return!1;var s=this;return this._timer=setTimeout(function(){s._attempts++,s._operationTimeoutCb&&(s._timeout=setTimeout(function(){s._operationTimeoutCb(s._attempts)},s._operationTimeout),s._options.unref&&s._timeout.unref()),s._fn(s._attempts)},t),this._options.unref&&this._timer.unref(),!0};ce.prototype.attempt=function(r,e){this._fn=r,e&&(e.timeout&&(this._operationTimeout=e.timeout),e.cb&&(this._operationTimeoutCb=e.cb));var t=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){t._operationTimeoutCb()},t._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)};ce.prototype.try=function(r){console.log("Using RetryOperation.try() is deprecated"),this.attempt(r)};ce.prototype.start=function(r){console.log("Using RetryOperation.start() is deprecated"),this.attempt(r)};ce.prototype.start=ce.prototype.try;ce.prototype.errors=function(){return this._errors};ce.prototype.attempts=function(){return this._attempts};ce.prototype.mainError=function(){if(this._errors.length===0)return null;for(var r={},e=null,t=0,s=0;s<this._errors.length;s++){var i=this._errors[s],n=i.message,o=(r[n]||0)+1;r[n]=o,o>=t&&(e=i,t=o)}return e}});var jc=E(Ft=>{u();var Mp=Ic();Ft.operation=function(r){var e=Ft.timeouts(r);return new Mp(e,{forever:r&&(r.forever||r.retries===1/0),unref:r&&r.unref,maxRetryTime:r&&r.maxRetryTime})};Ft.timeouts=function(r){if(r instanceof Array)return[].concat(r);var e={retries:10,factor:2,minTimeout:1*1e3,maxTimeout:1/0,randomize:!1};for(var t in r)e[t]=r[t];if(e.minTimeout>e.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var s=[],i=0;i<e.retries;i++)s.push(this.createTimeout(i,e));return r&&r.forever&&!s.length&&s.push(this.createTimeout(i,e)),s.sort(function(n,o){return n-o}),s};Ft.createTimeout=function(r,e){var t=e.randomize?Math.random()+1:1,s=Math.round(t*Math.max(e.minTimeout,1)*Math.pow(e.factor,r));return s=Math.min(s,e.maxTimeout),s};Ft.wrap=function(r,e,t){if(e instanceof Array&&(t=e,e=null),!t){t=[];for(var s in r)typeof r[s]=="function"&&t.push(s)}for(var i=0;i<t.length;i++){var n=t[i],o=r[n];r[n]=function(l){var c=Ft.operation(e),h=Array.prototype.slice.call(arguments,1),m=h.pop();h.push(function(D){c.retry(D)||(D&&(arguments[0]=c.mainError()),m.apply(this,arguments))}),c.attempt(function(){l.apply(r,h)})}.bind(r,o),r[n].options=e}}});var Uc=E((k0,qc)=>{u();qc.exports=jc()});var Wc=E((_0,$c)=>{u();var Ip=Uc();function jp(r,e){function t(s,i){var n=e||{},o;"randomize"in n||(n.randomize=!0),o=Ip.operation(n);function a(h){i(h||new Error("Aborted"))}function l(h,m){if(h.bail){a(h);return}o.retry(h)?n.onRetry&&n.onRetry(h,m):i(o.mainError())}function c(h){var m;try{m=r(a,h)}catch(D){l(D,h);return}Promise.resolve(m).then(s).catch(function(A){l(A,h)})}o.attempt(c)}return new Promise(t)}$c.exports=jp});var ym={};wl(ym,{ConvertError:()=>R,MANAGERS:()=>Ue,convert:()=>Em,getPackageManagerMeta:()=>ro,getWorkspaceDetails:()=>to,install:()=>Hs});module.exports=Fl(ym);u();u();u();u();var Ol=F(ho());u();u();function Qs(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}u();u();function ar(r){return ar=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ar(r)}u();function ei(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function ti(r,e){if(e&&(ar(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ei(r)}u();function lt(r){return lt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},lt(r)}u();u();function Se(r,e){return Se=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(s,i){return s.__proto__=i,s},Se(r,e)}function ri(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&Se(r,e)}u();u();function si(r){return Function.toString.call(r).indexOf("[native code]")!==-1}u();u();function ii(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function vt(r,e,t){return ii()?vt=Reflect.construct.bind():vt=function(i,n,o){var a=[null];a.push.apply(a,n);var l=Function.bind.apply(i,a),c=new l;return o&&Se(c,o.prototype),c},vt.apply(null,arguments)}function cr(r){var e=typeof Map=="function"?new Map:void 0;return cr=function(s){if(s===null||!si(s))return s;if(typeof s!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e<"u"){if(e.has(s))return e.get(s);e.set(s,i)}function i(){return vt(s,arguments,lt(this).constructor)}return i.prototype=Object.create(s.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),Se(i,s)},cr(r)}var vo=F(Bo());var Tl=F(require("fs-extra")),nd=function(r){ri(e,r);function e(t){var s;return Qs(this,e),s=ti(this,lt(e).call(this,"No package.json could be found upwards from the directory ".concat(t))),s.directory=t,s}return e}(cr(Error));u();u();var Pl=F(require("fs")),Nl=F(require("path"));u();var Wl=F(require("fs")),zl=F(require("path")),Gl=F(require("js-yaml")),Hl=require("fast-glob");u();var Ot=require("picocolors"),Il=F(require("ora")),xo=F(require("gradient-string")),Oo="#0099F7",To="#F11712",jl="#FFFF00",hi=r=>{let e=ql(r);return t=>`\x1B[38;5;${e}m${t}${(0,Ot.reset)("")}`},md=(0,xo.default)(Oo,To),dd=hi(Oo),Dd=hi(To),gd=hi(jl);function ql(r){let e=parseInt(r.slice(1),16),t=Math.floor(e/(256*256))%256,s=Math.floor(e/256)%256,i=e%256;return 16+36*Math.round(t/255*5)+6*Math.round(s/255*5)+Math.round(i/255*5)}u();var Po=F(require("os")),No=F(require("execa"));async function jr(r,e=[],t){let s={cwd:Po.default.tmpdir(),env:{COREPACK_ENABLE_STRICT:"0"},...t};try{let{stdout:i}=await(0,No.default)(r,e,s);return i.trim()}catch{return}}async function pi(){let[r,e,t,s]=await Promise.all([jr("yarnpkg",["--version"],{cwd:"."}),jr("npm",["--version"]),jr("pnpm",["--version"]),jr("bun",["--version"])]);return{yarn:r,pnpm:t,npm:e,bun:s}}u();var Jl=F(require("fs-extra"));u();var Vl=F(require("path")),Yl=F(require("fs-extra")),Kl=F(require("picocolors"));u();var Pc=require("stream"),Nc=require("util"),Tp=require("path"),Pp=require("os"),Lc=require("fs"),Np=F(Tc());var C0=(0,Nc.promisify)(Pc.Stream.pipeline);u();var Lp=F(require("fs-extra"));u();var Hp=F(require("path")),Jp=F(Wc()),Vp=F(require("picocolors")),Yp=F(require("fs-extra"));u();u();u();u();var R=class extends Error{constructor(t,s){var i;super(t);this.name="ConvertError",this.type=(i=s==null?void 0:s.type)!=null?i:"unknown",Error.captureStackTrace(this,R)}};u();u();var bt=F(require("path")),me=F(require("fs-extra")),Xc=F(require("execa"));u();var Yc=F(require("path")),Kc=F(require("fs-extra")),Zc=F(require("picocolors"));u();var je=F(require("path")),zc=F(require("execa")),te=require("fs-extra"),Gc=require("fast-glob"),Hc=F(require("js-yaml"));var Kp=/^(?!_)(?<manager>.+)@(?<version>.+)$/;function M({workspaceRoot:r}){let e=je.default.join(r,"package.json");try{return(0,te.readJsonSync)(e,"utf8")}catch(t){if(t&&typeof t=="object"&&"code"in t){if(t.code==="ENOENT")throw new R(`no "package.json" found at ${r}`,{type:"package_json-missing"});if(t.code==="EJSONPARSE")throw new R(`failed to parse "package.json" at ${r}`,{type:"package_json-parse_error"})}throw new Error(`unexpected error reading "package.json" at ${r}`)}}function st({workspaceRoot:r}){let{packageManager:e}=M({workspaceRoot:r});if(e)try{let t=Kp.exec(e);if(t){let[s,i]=t;return i}}catch{}}function qe({workspaceRoot:r}){let e=M({workspaceRoot:r}),t=je.default.basename(r),{name:s=t,description:i}=e;return{name:s,description:i}}function eo({workspaceRoot:r}){let e=je.default.join(r,"pnpm-workspace.yaml");if((0,te.existsSync)(e))try{let t=Hc.default.load((0,te.readFileSync)(e,"utf8"));if(t instanceof Object&&"packages"in t&&Array.isArray(t.packages))return t.packages}catch{throw new R(`failed to parse ${e}`,{type:"pnpm-workspace_parse_error"})}return[]}function it({root:r,lockFile:e,workspaceConfig:t}){let s=n=>je.default.join(r,n),i={root:r,lockfile:s(e),packageJson:s("package.json"),nodeModules:s("node_modules")};return t&&(i.workspaceConfig=s(t)),i}function rr({workspaces:r}){var e;return r?Array.isArray(r)?r:"packages"in r?(e=r.packages)!=null?e:[]:[]:[]}function nt({workspaceRoot:r,workspaceGlobs:e}){if(!e)return[];let t=e.filter(s=>s.startsWith("!")).map(s=>s.slice(1));return e.filter(s=>!s.startsWith("!")).flatMap(s=>{let i=[`${s}/package.json`];return(0,Gc.sync)(i,{onlyFiles:!0,absolute:!0,cwd:r,ignore:["**/node_modules/**",...t]})}).map(s=>{let i=je.default.dirname(s),{name:n,description:o}=qe({workspaceRoot:i});return{name:n,description:o,paths:{root:i,packageJson:s,nodeModules:je.default.join(i,"node_modules")}}})}function Jc({directory:r}){let e=je.default.resolve(process.cwd(),r);return{exists:(0,te.existsSync)(e),absolute:e}}function le({packageManager:r,action:e,project:t}){let s=t.workspaceData.globs.length>0;return`${e==="remove"?"Removing":"Adding"} ${r} ${s?"workspaces":""} ${e==="remove"?"from":"to"} ${t.name}`}function Vc({project:r}){let e=t=>!(t.includes("*")&&(t.includes("**")||t.split("/").slice(0,-1).join("/").includes("*"))||["!","[","]","{","}"].some(s=>t.includes(s)));return r.workspaceData.globs.every(e)}function re({project:r,options:e}){e!=null&&e.dry||(0,te.rmSync)(r.paths.lockfile,{force:!0})}async function Gs({project:r,options:e}){if(!(e!=null&&e.dry)&&(0,te.existsSync)(r.paths.lockfile))try{let{stdout:t}=await(0,zc.default)("bun",["bun.lockb"],{stdin:"ignore",cwd:r.paths.root});await(0,te.writeFile)(je.default.join(r.paths.root,"yarn.lock"),t)}catch{}finally{(0,te.rmSync)(r.paths.lockfile,{force:!0})}}function Zp({dependencyList:r,project:e,to:t}){let s=[];return e.workspaceData.workspaces.forEach(i=>{let{name:n}=i;if(r[n]){let o=r[n],a=o.startsWith("workspace:")?o.slice(10):o;r[n]=t.name==="pnpm"?`workspace:${a}`:a,s.push(n)}}),{dependencyList:r,updated:s}}function he({project:r,workspace:e,to:t,logger:s,options:i}){if(["yarn","npm","bun"].includes(t.name)&&["yarn","npm","bun"].includes(r.packageManager))return;let n=M({workspaceRoot:e.paths.root}),o={dependencies:[],devDependencies:[],peerDependencies:[],optionalDependencies:[]},a=["dependencies","devDependencies","peerDependencies","optionalDependencies"];a.forEach(m=>{let D=n[m];if(D){let{updated:A,dependencyList:b}=Zp({dependencyList:D,project:r,to:t});n[m]=b,o[m]=A}});let l=m=>{let D=o[m].length;if(D>0)return`${Zc.default.green(D.toString())} ${m}`},c=a.map(l).filter(Boolean),h=`./${Yc.default.relative(r.paths.root,e.paths.packageJson)}`;if(c.length>=1){let m="updating";c.forEach((D,A)=>{c.length===1?m+=` ${D} in ${h}`:A===c.length-1?m+=`and ${D} in ${h}`:m+=` ${D}, `}),s.workspaceStep(m)}else s.workspaceStep(`no workspace dependencies found in ${h}`);i!=null&&i.dry||Kc.default.writeJSONSync(e.paths.packageJson,n,{spaces:2})}var ot={name:"pnpm",lock:"pnpm-lock.yaml"};async function Qc(r){let e=bt.default.join(r.workspaceRoot,ot.lock),t=bt.default.join(r.workspaceRoot,"pnpm-workspace.yaml"),s=st({workspaceRoot:r.workspaceRoot});return me.default.existsSync(e)||me.default.existsSync(t)||s===ot.name}async function Xp(r){if(!await Qc(r))throw new R("Not a pnpm project",{type:"package_manager-unexpected"});let{name:t,description:s}=qe(r);return{name:t,description:s,packageManager:ot.name,paths:it({root:r.workspaceRoot,lockFile:ot.lock,workspaceConfig:"pnpm-workspace.yaml"}),workspaceData:{globs:eo(r),workspaces:nt({workspaceGlobs:eo(r),...r})}}}async function Qp(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;s.mainStep(le({action:"create",packageManager:ot.name,project:e}));let o=M({workspaceRoot:e.paths.root});s.rootHeader(),o.packageManager=`${t.name}@${t.version}`,s.rootStep(`adding "packageManager" field to ${e.name} root "package.json"`),i!=null&&i.dry||(me.default.writeJSONSync(e.paths.packageJson,o,{spaces:2}),n&&(s.rootStep('adding "pnpm-workspace.yaml"'),me.default.writeFileSync(bt.default.join(e.paths.root,"pnpm-workspace.yaml"),`packages:
${e.workspaceData.globs.map(a=>`  - "${a}"`).join(`
`)}`))),n&&(he({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{he({workspace:a,project:e,to:t,logger:s,options:i})}))}async function em(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(le({action:"remove",packageManager:ot.name,project:e}));let n=M({workspaceRoot:e.paths.root});if(e.paths.workspaceConfig&&i&&(t.subStep('removing "pnpm-workspace.yaml"'),s!=null&&s.dry||me.default.rmSync(e.paths.workspaceConfig,{force:!0})),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){me.default.writeJSONSync(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>me.default.rm(a,{recursive:!0,force:!0})))}catch{throw new R("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function tm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${bt.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||me.default.rmSync(e.paths.lockfile,{force:!0})}async function rm(r){let{project:e,options:t,logger:s}=r,i=()=>{s.subStep(`converting ${bt.default.relative(e.paths.root,e.paths.lockfile)} to ${ot.lock}`)},n=async()=>{if(!(t!=null&&t.dry)&&me.default.existsSync(e.paths.lockfile))try{await(0,Xc.default)(ot.name,["import"],{stdio:"ignore",cwd:e.paths.root})}catch{}finally{re({project:e,options:t})}};switch(e.packageManager){case"pnpm":break;case"bun":i(),await Gs({project:e,options:t}),await n(),me.default.rmSync(bt.default.join(e.paths.root,"yarn.lock"),{force:!0});break;case"npm":i(),await n();break;case"yarn":i(),await n();break}}var el={detect:Qc,read:Xp,create:Qp,remove:em,clean:tm,convertLock:rm};u();var vr=F(require("path")),St=F(require("fs-extra"));var sr={name:"npm",lock:"package-lock.json"};async function tl(r){let e=vr.default.join(r.workspaceRoot,sr.lock),t=st({workspaceRoot:r.workspaceRoot});return St.default.existsSync(e)||t===sr.name}async function sm(r){if(!await tl(r))throw new R("Not an npm project",{type:"package_manager-unexpected"});let t=M(r),{name:s,description:i}=qe(r),n=rr({workspaces:t.workspaces});return{name:s,description:i,packageManager:sr.name,paths:it({root:r.workspaceRoot,lockFile:sr.lock}),workspaceData:{globs:n,workspaces:nt({workspaceGlobs:n,...r})}}}async function im(r){let{project:e,options:t,to:s,logger:i}=r,n=e.workspaceData.globs.length>0;i.mainStep(le({packageManager:sr.name,action:"create",project:e}));let o=M({workspaceRoot:e.paths.root});i.rootHeader(),i.rootStep(`adding "packageManager" field to ${vr.default.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${s.name}@${s.version}`,n?(i.rootStep(`adding "workspaces" field to ${vr.default.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,t!=null&&t.dry||St.default.writeJSONSync(e.paths.packageJson,o,{spaces:2}),he({workspace:{name:"root",paths:e.paths},project:e,to:s,logger:i,options:t}),i.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{he({workspace:a,project:e,to:s,logger:i,options:t})})):t!=null&&t.dry||St.default.writeJSONSync(e.paths.packageJson,o,{spaces:2})}async function nm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(le({packageManager:sr.name,action:"remove",project:e}));let n=M({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){St.default.writeJSONSync(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>St.default.rm(a,{recursive:!0,force:!0})))}catch{throw new R("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function om(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${vr.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||St.default.rmSync(e.paths.lockfile,{force:!0})}async function um(r){let{project:e,options:t}=r;switch(e.packageManager){case"pnpm":re({project:e,options:t});break;case"bun":re({project:e,options:t});break;case"npm":break;case"yarn":re({project:e,options:t});break}}var rl={detect:tl,read:sm,create:im,remove:nm,clean:om,convertLock:um};u();var ir=F(require("path")),kt=F(require("fs-extra"));var _t={name:"yarn",lock:"yarn.lock"};async function sl(r){let e=ir.default.join(r.workspaceRoot,_t.lock),t=st({workspaceRoot:r.workspaceRoot});return kt.default.existsSync(e)||t===_t.name}async function am(r){if(!await sl(r))throw new R("Not a yarn project",{type:"package_manager-unexpected"});let t=M(r),{name:s,description:i}=qe(r),n=rr({workspaces:t.workspaces});return{name:s,description:i,packageManager:_t.name,paths:it({root:r.workspaceRoot,lockFile:_t.lock}),workspaceData:{globs:n,workspaces:nt({workspaceGlobs:n,...r})}}}async function cm(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;s.mainStep(le({packageManager:_t.name,action:"create",project:e}));let o=M({workspaceRoot:e.paths.root});s.rootHeader(),s.rootStep(`adding "packageManager" field to ${ir.default.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${t.name}@${t.version}`,n?(s.rootStep(`adding "workspaces" field to ${ir.default.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,i!=null&&i.dry||kt.default.writeJSONSync(e.paths.packageJson,o,{spaces:2}),he({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{he({workspace:a,project:e,to:t,logger:s,options:i})})):i!=null&&i.dry||kt.default.writeJSONSync(e.paths.packageJson,o,{spaces:2})}async function lm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(le({packageManager:_t.name,action:"remove",project:e}));let n=M({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){kt.default.writeJSONSync(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>kt.default.rm(a,{recursive:!0,force:!0})))}catch{throw new R("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function hm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${ir.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||kt.default.rmSync(e.paths.lockfile,{force:!0})}async function fm(r){let{project:e,options:t,logger:s}=r,i=()=>{s.subStep(`converting ${ir.default.relative(e.paths.root,e.paths.lockfile)} to ${_t.lock}`)};switch(e.packageManager){case"pnpm":re({project:e,options:t});break;case"bun":i(),await Gs({project:e,options:t});break;case"npm":re({project:e,options:t});break;case"yarn":break}}var il={detect:sl,read:am,create:cm,remove:lm,clean:hm,convertLock:fm};u();var xr=F(require("path")),Rt=F(require("fs-extra"));var nr={name:"bun",lock:"bun.lockb"};async function nl(r){let e=xr.default.join(r.workspaceRoot,nr.lock),t=st({workspaceRoot:r.workspaceRoot});return Rt.default.existsSync(e)||t===nr.name}async function pm(r){if(!await nl(r))throw new R("Not a bun project",{type:"package_manager-unexpected"});let t=M(r),{name:s,description:i}=qe(r),n=rr({workspaces:t.workspaces});return{name:s,description:i,packageManager:nr.name,paths:it({root:r.workspaceRoot,lockFile:nr.lock}),workspaceData:{globs:n,workspaces:nt({workspaceGlobs:n,...r})}}}async function mm(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;if(!Vc({project:e}))throw new R("Unable to convert project to bun - workspace globs unsupported",{type:"bun-workspace_glob_error"});s.mainStep(le({packageManager:nr.name,action:"create",project:e}));let o=M({workspaceRoot:e.paths.root});s.rootHeader(),s.rootStep(`adding "packageManager" field to ${xr.default.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${t.name}@${t.version}`,n?(s.rootStep(`adding "workspaces" field to ${xr.default.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,i!=null&&i.dry||Rt.default.writeJSONSync(e.paths.packageJson,o,{spaces:2}),he({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{he({workspace:a,project:e,to:t,logger:s,options:i})})):i!=null&&i.dry||Rt.default.writeJSONSync(e.paths.packageJson,o,{spaces:2})}async function dm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(le({packageManager:nr.name,action:"remove",project:e}));let n=M({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){Rt.default.writeJSONSync(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>Rt.default.rm(a,{recursive:!0,force:!0})))}catch{throw new R("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function Dm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${xr.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||Rt.default.rmSync(e.paths.lockfile,{force:!0})}async function gm(r){let{project:e,options:t}=r;switch(e.packageManager){case"pnpm":re({project:e,options:t});break;case"bun":break;case"npm":re({project:e,options:t});break;case"yarn":re({project:e,options:t});break}}var ol={detect:nl,read:pm,create:mm,remove:dm,clean:Dm,convertLock:gm};var Ue={pnpm:el,yarn:il,npm:rl,bun:ol};async function to({root:r}){let{exists:e,absolute:t}=Jc({directory:r});if(!e)throw new R(`Could not find directory at ${t}. Ensure the directory exists.`,{type:"invalid_directory"});for(let{detect:s,read:i}of Object.values(Ue))if(await s({workspaceRoot:t}))return i({workspaceRoot:t});throw new R("Could not determine package manager. Add `packageManager` to `package.json` or ensure a lockfile is present.",{type:"package_manager-unable_to_detect"})}u();var fl=F(require("picocolors"));u();var cl=F(require("execa")),ll=F(require("ora")),hl=require("semver");u();var se=F(require("picocolors")),ul=F(require("gradient-string")),or=2,ur=class{constructor({interactive:e,dry:t}={}){this.interactive=e!=null?e:!0,this.dry=t!=null?t:!1,this.step=1}logger(...e){this.interactive&&console.log(...e)}indented(e,...t){this.logger(" ".repeat(or*e),...t)}header(e){this.blankLine(),this.logger(se.default.bold(e))}installerFrames(){let e=`${" ".repeat(or)} - ${this.dry?se.default.yellow("SKIPPED | "):se.default.green("OK | ")}`;return[`${e}   `,`${e}>  `,`${e}>> `,`${e}>>>`]}gradient(e){return(0,ul.default)("#0099F7","#F11712")(e.toString())}hero(){this.logger(se.default.bold(this.gradient(`
>>> TURBOREPO
`)))}info(...e){this.logger(...e)}mainStep(e){this.blankLine(),this.logger(`${this.step}. ${se.default.underline(e)}`),this.step+=1}subStep(...e){this.logger(" ".repeat(or),"-",this.dry?se.default.yellow("SKIPPED |"):se.default.green("OK |"),...e)}subStepFailure(...e){this.logger(" ".repeat(or),"-",se.default.red("ERROR |"),...e)}rootHeader(){this.blankLine(),this.indented(2,"Root:")}rootStep(...e){this.logger(" ".repeat(or*3),"-",this.dry?se.default.yellow("SKIPPED |"):se.default.green("OK |"),...e)}workspaceHeader(){this.blankLine(),this.indented(2,"Workspaces:")}workspaceStep(...e){this.logger(" ".repeat(or*3),"-",this.dry?se.default.yellow("SKIPPED |"):se.default.green("OK |"),...e)}blankLine(){this.logger()}error(...e){console.error(...e)}};var al={npm:[{name:"npm",template:"npm",command:"npm",installArgs:["install"],version:"latest",executable:"npx",semver:"*",default:!0}],pnpm:[{name:"pnpm6",template:"pnpm",command:"pnpm",installArgs:["install"],version:"latest-6",executable:"pnpx",semver:"6.x"},{name:"pnpm",template:"pnpm",command:"pnpm",installArgs:["install","--fix-lockfile"],version:"latest",executable:"pnpm dlx",semver:">=7",default:!0}],yarn:[{name:"yarn",template:"yarn",command:"yarn",installArgs:["install"],version:"1.x",executable:"npx",semver:"<2",default:!0},{name:"berry",template:"berry",command:"yarn",installArgs:["install","--no-immutable"],version:"stable",executable:"yarn dlx",semver:">=2"}],bun:[{name:"bun",template:"bun",command:"bun",installArgs:["install"],version:"latest",executable:"bunx",semver:"^1.0.1",default:!0}]};function ro(r){let{version:e,name:t}=r;return e?al[t].find(s=>(0,hl.satisfies)(e,s.semver)):al[t].find(s=>s.default)}async function Hs(r){let{to:e,logger:t,options:s}=r,i=t!=null?t:new ur(s),n=ro(e);if(!n)throw new R("Unsupported package manager version.",{type:"package_manager-unsupported_version"});if(i.subStep(`running "${n.command} ${n.installArgs.join(" ")}"`),!(s!=null&&s.dry)){let o;i.interactive&&(o=(0,ll.default)({text:"installing dependencies...",spinner:{frames:i.installerFrames()}}).start());try{await(0,cl.default)(n.command,n.installArgs,{cwd:r.project.paths.root}),o&&o.stop(),i.subStep("dependencies installed")}catch(a){throw i.subStepFailure("failed to install dependencies"),a}}}async function pl({project:r,convertTo:e,logger:t,options:s}){if(t.header(`Converting project from ${r.packageManager} to ${e.name}.`),!(s!=null&&s.ignoreUnchangedPackageManager)){if(r.packageManager===e.name)throw new R("You are already using this package manager",{type:"package_manager-already_in_use"});if(!e.version)throw new R(`${e.name} is not installed, or could not be located`,{type:"package_manager-could_not_be_found"})}let i=e;s!=null&&s.ignoreUnchangedPackageManager||await Ue[r.packageManager].remove({project:r,to:i,logger:t,options:s}),await Ue[i.name].create({project:r,to:i,logger:t,options:s}),t.mainStep("Installing dependencies"),s!=null&&s.skipInstall?t.subStep(fl.default.yellow("Skipping install")):(await Ue[i.name].convertLock({project:r,to:i,logger:t,options:s}),await Hs({project:r,to:i,logger:t,options:s})),t.mainStep(`Cleaning up ${r.packageManager} workspaces`),r.packageManager!==e.name&&await Ue[r.packageManager].clean({project:r,logger:t})}async function Em({root:r,to:e,options:t}){let s=new ur({...t,interactive:!1}),[i,n]=await Promise.all([to({root:r}),pi()]);await pl({project:i,convertTo:{name:e,version:n[e]},logger:s,options:t})}0&&(module.exports={ConvertError,MANAGERS,convert,getPackageManagerMeta,getWorkspaceDetails,install});
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
