#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@turbo+workspaces@2.5.5_@types+node@24.2.1/node_modules/@turbo/workspaces/dist/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@turbo+workspaces@2.5.5_@types+node@24.2.1/node_modules/@turbo/workspaces/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@turbo+workspaces@2.5.5_@types+node@24.2.1/node_modules/@turbo/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@turbo+workspaces@2.5.5_@types+node@24.2.1/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@turbo+workspaces@2.5.5_@types+node@24.2.1/node_modules/@turbo/workspaces/dist/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@turbo+workspaces@2.5.5_@types+node@24.2.1/node_modules/@turbo/workspaces/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@turbo+workspaces@2.5.5_@types+node@24.2.1/node_modules/@turbo/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@turbo+workspaces@2.5.5_@types+node@24.2.1/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/cli.js" "$@"
else
  exec node  "$basedir/../../dist/cli.js" "$@"
fi
