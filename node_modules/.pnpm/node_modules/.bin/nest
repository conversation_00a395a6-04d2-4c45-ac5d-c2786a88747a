#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@nestjs+cli@10.4.9/node_modules/@nestjs/cli/bin/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@nestjs+cli@10.4.9/node_modules/@nestjs/cli/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@nestjs+cli@10.4.9/node_modules/@nestjs/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@nestjs+cli@10.4.9/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@nestjs+cli@10.4.9/node_modules/@nestjs/cli/bin/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@nestjs+cli@10.4.9/node_modules/@nestjs/cli/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@nestjs+cli@10.4.9/node_modules/@nestjs/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/@nestjs+cli@10.4.9/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@nestjs/cli/bin/nest.js" "$@"
else
  exec node  "$basedir/../@nestjs/cli/bin/nest.js" "$@"
fi
