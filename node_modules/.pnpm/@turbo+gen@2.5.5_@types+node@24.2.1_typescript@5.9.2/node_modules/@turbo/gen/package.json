{"name": "@turbo/gen", "version": "2.5.5", "description": "Extend a Turborepo", "homepage": "https://turborepo.com", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vercel/turborepo", "directory": "packages/turbo-gen"}, "bugs": {"url": "https://github.com/vercel/turborepo/issues"}, "bin": "dist/cli.js", "types": "dist/types.d.ts", "dependencies": {"commander": "^10.0.0", "fs-extra": "^10.1.0", "inquirer": "^8.2.4", "minimatch": "^9.0.0", "node-plop": "^0.26.3", "picocolors": "1.0.1", "proxy-agent": "^6.5.0", "ts-node": "^10.9.2", "update-check": "^1.5.4", "validate-npm-package-name": "^5.0.0", "@turbo/workspaces": "2.5.5"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/fs-extra": "^9.0.13", "@types/inquirer": "^8.2.5", "@types/node": "^18.17.2", "@types/validate-npm-package-name": "^4.0.0", "jest": "^29.7.0", "ts-jest": "^29.2.5", "tsup": "^6.7.0", "typescript": "5.5.4", "@turbo/eslint-config": "0.0.0", "@turbo/test-utils": "0.0.0", "@turbo/tsconfig": "0.0.0", "@turbo/utils": "0.0.0"}, "files": ["dist"], "publishConfig": {"access": "public"}, "scripts": {"build": "tsup", "test": "jest", "lint": "eslint src/", "check-types": "tsc --noEmit"}}