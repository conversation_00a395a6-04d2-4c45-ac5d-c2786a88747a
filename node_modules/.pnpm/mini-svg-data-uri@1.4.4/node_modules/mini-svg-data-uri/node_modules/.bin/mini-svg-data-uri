#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/mini-svg-data-uri@1.4.4/node_modules/mini-svg-data-uri/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/mini-svg-data-uri@1.4.4/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/mini-svg-data-uri@1.4.4/node_modules/mini-svg-data-uri/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/mini-svg-data-uri@1.4.4/node_modules:/Users/<USER>/Data/new era/CanSell-Ultimate/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../cli.js" "$@"
else
  exec node  "$basedir/../../cli.js" "$@"
fi
