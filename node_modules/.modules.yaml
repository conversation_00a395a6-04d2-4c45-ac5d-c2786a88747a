hoistPattern:
  - '*'
hoistedDependencies:
  /@alloc/quick-lru/5.2.0:
    '@alloc/quick-lru': private
  /@babel/runtime/7.28.2:
    '@babel/runtime': private
  /@eslint-community/eslint-utils/4.7.0(eslint@8.57.1):
    '@eslint-community/eslint-utils': public
  /@eslint-community/regexpp/4.12.1:
    '@eslint-community/regexpp': public
  /@eslint/eslintrc/2.1.4:
    '@eslint/eslintrc': public
  /@eslint/js/8.57.1:
    '@eslint/js': public
  /@floating-ui/core/1.7.3:
    '@floating-ui/core': private
  /@floating-ui/dom/1.7.3:
    '@floating-ui/dom': private
  /@floating-ui/react-dom/2.1.5(react-dom@18.3.1)(react@18.3.1):
    '@floating-ui/react-dom': private
  /@floating-ui/utils/0.2.10:
    '@floating-ui/utils': private
  /@hookform/resolvers/3.10.0(react-hook-form@7.62.0):
    '@hookform/resolvers': private
  /@humanwhocodes/config-array/0.13.0:
    '@humanwhocodes/config-array': private
  /@humanwhocodes/module-importer/1.0.1:
    '@humanwhocodes/module-importer': private
  /@humanwhocodes/object-schema/2.0.3:
    '@humanwhocodes/object-schema': private
  /@img/sharp-darwin-arm64/0.34.3:
    '@img/sharp-darwin-arm64': private
  /@img/sharp-darwin-x64/0.34.3:
    '@img/sharp-darwin-x64': private
  /@img/sharp-libvips-darwin-arm64/1.2.0:
    '@img/sharp-libvips-darwin-arm64': private
  /@img/sharp-libvips-darwin-x64/1.2.0:
    '@img/sharp-libvips-darwin-x64': private
  /@img/sharp-libvips-linux-arm/1.2.0:
    '@img/sharp-libvips-linux-arm': private
  /@img/sharp-libvips-linux-arm64/1.2.0:
    '@img/sharp-libvips-linux-arm64': private
  /@img/sharp-libvips-linux-ppc64/1.2.0:
    '@img/sharp-libvips-linux-ppc64': private
  /@img/sharp-libvips-linux-s390x/1.2.0:
    '@img/sharp-libvips-linux-s390x': private
  /@img/sharp-libvips-linux-x64/1.2.0:
    '@img/sharp-libvips-linux-x64': private
  /@img/sharp-libvips-linuxmusl-arm64/1.2.0:
    '@img/sharp-libvips-linuxmusl-arm64': private
  /@img/sharp-libvips-linuxmusl-x64/1.2.0:
    '@img/sharp-libvips-linuxmusl-x64': private
  /@img/sharp-linux-arm/0.34.3:
    '@img/sharp-linux-arm': private
  /@img/sharp-linux-arm64/0.34.3:
    '@img/sharp-linux-arm64': private
  /@img/sharp-linux-ppc64/0.34.3:
    '@img/sharp-linux-ppc64': private
  /@img/sharp-linux-s390x/0.34.3:
    '@img/sharp-linux-s390x': private
  /@img/sharp-linux-x64/0.34.3:
    '@img/sharp-linux-x64': private
  /@img/sharp-linuxmusl-arm64/0.34.3:
    '@img/sharp-linuxmusl-arm64': private
  /@img/sharp-linuxmusl-x64/0.34.3:
    '@img/sharp-linuxmusl-x64': private
  /@img/sharp-wasm32/0.34.3:
    '@img/sharp-wasm32': private
  /@img/sharp-win32-arm64/0.34.3:
    '@img/sharp-win32-arm64': private
  /@img/sharp-win32-ia32/0.34.3:
    '@img/sharp-win32-ia32': private
  /@img/sharp-win32-x64/0.34.3:
    '@img/sharp-win32-x64': private
  /@isaacs/cliui/8.0.2:
    '@isaacs/cliui': private
  /@jridgewell/gen-mapping/0.3.13:
    '@jridgewell/gen-mapping': private
  /@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  /@jridgewell/sourcemap-codec/1.5.5:
    '@jridgewell/sourcemap-codec': private
  /@jridgewell/trace-mapping/0.3.30:
    '@jridgewell/trace-mapping': private
  /@next/env/15.4.6:
    '@next/env': private
  /@next/eslint-plugin-next/15.4.6:
    '@next/eslint-plugin-next': public
  /@next/swc-darwin-arm64/15.4.6:
    '@next/swc-darwin-arm64': private
  /@next/swc-darwin-x64/15.4.6:
    '@next/swc-darwin-x64': private
  /@next/swc-linux-arm64-gnu/15.4.6:
    '@next/swc-linux-arm64-gnu': private
  /@next/swc-linux-arm64-musl/15.4.6:
    '@next/swc-linux-arm64-musl': private
  /@next/swc-linux-x64-gnu/15.4.6:
    '@next/swc-linux-x64-gnu': private
  /@next/swc-linux-x64-musl/15.4.6:
    '@next/swc-linux-x64-musl': private
  /@next/swc-win32-arm64-msvc/15.4.6:
    '@next/swc-win32-arm64-msvc': private
  /@next/swc-win32-x64-msvc/15.4.6:
    '@next/swc-win32-x64-msvc': private
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@nolyfill/is-core-module/1.0.39:
    '@nolyfill/is-core-module': private
  /@panva/hkdf/1.2.1:
    '@panva/hkdf': private
  /@pkgjs/parseargs/0.11.0:
    '@pkgjs/parseargs': private
  /@radix-ui/number/1.1.1:
    '@radix-ui/number': private
  /@radix-ui/primitive/1.1.3:
    '@radix-ui/primitive': private
  /@radix-ui/react-arrow/1.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-arrow': private
  /@radix-ui/react-avatar/1.1.10(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-avatar': private
  /@radix-ui/react-collection/1.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-collection': private
  /@radix-ui/react-compose-refs/1.1.2(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-compose-refs': private
  /@radix-ui/react-context/1.1.2(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-context': private
  /@radix-ui/react-dialog/1.1.15(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-dialog': private
  /@radix-ui/react-direction/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-direction': private
  /@radix-ui/react-dismissable-layer/1.1.11(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-dismissable-layer': private
  /@radix-ui/react-dropdown-menu/2.1.16(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-dropdown-menu': private
  /@radix-ui/react-focus-guards/1.1.3(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-focus-guards': private
  /@radix-ui/react-focus-scope/1.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-focus-scope': private
  /@radix-ui/react-id/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-id': private
  /@radix-ui/react-label/2.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-label': private
  /@radix-ui/react-menu/2.1.16(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-menu': private
  /@radix-ui/react-navigation-menu/1.2.14(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-navigation-menu': private
  /@radix-ui/react-popper/1.2.8(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-popper': private
  /@radix-ui/react-portal/1.1.9(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-portal': private
  /@radix-ui/react-presence/1.1.5(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-presence': private
  /@radix-ui/react-primitive/2.1.3(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-primitive': private
  /@radix-ui/react-roving-focus/1.1.11(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-roving-focus': private
  /@radix-ui/react-select/2.2.6(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-select': private
  /@radix-ui/react-separator/1.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-separator': private
  /@radix-ui/react-slot/1.2.3(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-slot': private
  /@radix-ui/react-tabs/1.1.13(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-tabs': private
  /@radix-ui/react-toast/1.2.15(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-toast': private
  /@radix-ui/react-use-callback-ref/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-callback-ref': private
  /@radix-ui/react-use-controllable-state/1.2.2(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-controllable-state': private
  /@radix-ui/react-use-effect-event/0.0.2(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-effect-event': private
  /@radix-ui/react-use-escape-keydown/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-escape-keydown': private
  /@radix-ui/react-use-is-hydrated/0.1.0(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-is-hydrated': private
  /@radix-ui/react-use-layout-effect/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-layout-effect': private
  /@radix-ui/react-use-previous/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-previous': private
  /@radix-ui/react-use-rect/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-rect': private
  /@radix-ui/react-use-size/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-size': private
  /@radix-ui/react-visually-hidden/1.2.3(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-visually-hidden': private
  /@radix-ui/rect/1.1.1:
    '@radix-ui/rect': private
  /@rtsao/scc/1.1.0:
    '@rtsao/scc': private
  /@rushstack/eslint-patch/1.12.0:
    '@rushstack/eslint-patch': public
  /@swc/helpers/0.5.15:
    '@swc/helpers': private
  /@tailwindcss/forms/0.5.10(tailwindcss@3.4.17):
    '@tailwindcss/forms': private
  /@tailwindcss/typography/0.5.16(tailwindcss@3.4.17):
    '@tailwindcss/typography': private
  /@tanstack/query-core/5.83.1:
    '@tanstack/query-core': private
  /@tanstack/react-query/5.85.0(react@18.3.1):
    '@tanstack/react-query': private
  /@types/json5/0.0.29:
    '@types/json5': private
  /@types/node/20.19.10:
    '@types/node': private
  /@types/prop-types/15.7.15:
    '@types/prop-types': private
  /@types/react-dom/18.3.7(@types/react@18.3.23):
    '@types/react-dom': private
  /@types/react/18.3.23:
    '@types/react': private
  /@typescript-eslint/eslint-plugin/8.39.1(@typescript-eslint/parser@8.39.1)(eslint@8.57.1)(typescript@5.9.2):
    '@typescript-eslint/eslint-plugin': public
  /@typescript-eslint/parser/8.39.1(eslint@8.57.1)(typescript@5.9.2):
    '@typescript-eslint/parser': public
  /@typescript-eslint/project-service/8.39.1(typescript@5.9.2):
    '@typescript-eslint/project-service': public
  /@typescript-eslint/scope-manager/8.39.1:
    '@typescript-eslint/scope-manager': public
  /@typescript-eslint/tsconfig-utils/8.39.1(typescript@5.9.2):
    '@typescript-eslint/tsconfig-utils': public
  /@typescript-eslint/type-utils/8.39.1(eslint@8.57.1)(typescript@5.9.2):
    '@typescript-eslint/type-utils': public
  /@typescript-eslint/types/8.39.1:
    '@typescript-eslint/types': public
  /@typescript-eslint/typescript-estree/8.39.1(typescript@5.9.2):
    '@typescript-eslint/typescript-estree': public
  /@typescript-eslint/utils/8.39.1(eslint@8.57.1)(typescript@5.9.2):
    '@typescript-eslint/utils': public
  /@typescript-eslint/visitor-keys/8.39.1:
    '@typescript-eslint/visitor-keys': public
  /@ungap/structured-clone/1.3.0:
    '@ungap/structured-clone': private
  /@unrs/resolver-binding-android-arm-eabi/1.11.1:
    '@unrs/resolver-binding-android-arm-eabi': private
  /@unrs/resolver-binding-android-arm64/1.11.1:
    '@unrs/resolver-binding-android-arm64': private
  /@unrs/resolver-binding-darwin-arm64/1.11.1:
    '@unrs/resolver-binding-darwin-arm64': private
  /@unrs/resolver-binding-darwin-x64/1.11.1:
    '@unrs/resolver-binding-darwin-x64': private
  /@unrs/resolver-binding-freebsd-x64/1.11.1:
    '@unrs/resolver-binding-freebsd-x64': private
  /@unrs/resolver-binding-linux-arm-gnueabihf/1.11.1:
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  /@unrs/resolver-binding-linux-arm-musleabihf/1.11.1:
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  /@unrs/resolver-binding-linux-arm64-gnu/1.11.1:
    '@unrs/resolver-binding-linux-arm64-gnu': private
  /@unrs/resolver-binding-linux-arm64-musl/1.11.1:
    '@unrs/resolver-binding-linux-arm64-musl': private
  /@unrs/resolver-binding-linux-ppc64-gnu/1.11.1:
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  /@unrs/resolver-binding-linux-riscv64-gnu/1.11.1:
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  /@unrs/resolver-binding-linux-riscv64-musl/1.11.1:
    '@unrs/resolver-binding-linux-riscv64-musl': private
  /@unrs/resolver-binding-linux-s390x-gnu/1.11.1:
    '@unrs/resolver-binding-linux-s390x-gnu': private
  /@unrs/resolver-binding-linux-x64-gnu/1.11.1:
    '@unrs/resolver-binding-linux-x64-gnu': private
  /@unrs/resolver-binding-linux-x64-musl/1.11.1:
    '@unrs/resolver-binding-linux-x64-musl': private
  /@unrs/resolver-binding-wasm32-wasi/1.11.1:
    '@unrs/resolver-binding-wasm32-wasi': private
  /@unrs/resolver-binding-win32-arm64-msvc/1.11.1:
    '@unrs/resolver-binding-win32-arm64-msvc': private
  /@unrs/resolver-binding-win32-ia32-msvc/1.11.1:
    '@unrs/resolver-binding-win32-ia32-msvc': private
  /@unrs/resolver-binding-win32-x64-msvc/1.11.1:
    '@unrs/resolver-binding-win32-x64-msvc': private
  /acorn-jsx/5.3.2(acorn@8.15.0):
    acorn-jsx: private
  /acorn/8.15.0:
    acorn: private
  /ajv/6.12.6:
    ajv: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/4.3.0:
    ansi-styles: private
  /any-promise/1.3.0:
    any-promise: private
  /anymatch/3.1.3:
    anymatch: private
  /arg/5.0.2:
    arg: private
  /argparse/2.0.1:
    argparse: private
  /aria-hidden/1.2.6:
    aria-hidden: private
  /aria-query/5.3.2:
    aria-query: private
  /array-buffer-byte-length/1.0.2:
    array-buffer-byte-length: private
  /array-includes/3.1.9:
    array-includes: private
  /array.prototype.findlast/1.2.5:
    array.prototype.findlast: private
  /array.prototype.findlastindex/1.2.6:
    array.prototype.findlastindex: private
  /array.prototype.flat/1.3.3:
    array.prototype.flat: private
  /array.prototype.flatmap/1.3.3:
    array.prototype.flatmap: private
  /array.prototype.tosorted/1.1.4:
    array.prototype.tosorted: private
  /arraybuffer.prototype.slice/1.0.4:
    arraybuffer.prototype.slice: private
  /ast-types-flow/0.0.8:
    ast-types-flow: private
  /async-function/1.0.0:
    async-function: private
  /autoprefixer/10.4.21(postcss@8.5.6):
    autoprefixer: private
  /available-typed-arrays/1.0.7:
    available-typed-arrays: private
  /axe-core/4.10.3:
    axe-core: private
  /axobject-query/4.1.0:
    axobject-query: private
  /balanced-match/1.0.2:
    balanced-match: private
  /binary-extensions/2.3.0:
    binary-extensions: private
  /brace-expansion/1.1.12:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /browserslist/4.25.2:
    browserslist: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bind/1.0.8:
    call-bind: private
  /call-bound/1.0.4:
    call-bound: private
  /callsites/3.1.0:
    callsites: private
  /camelcase-css/2.0.1:
    camelcase-css: private
  /caniuse-lite/1.0.30001734:
    caniuse-lite: private
  /chalk/4.1.2:
    chalk: private
  /chokidar/3.6.0:
    chokidar: private
  /class-variance-authority/0.7.1:
    class-variance-authority: private
  /client-only/0.0.1:
    client-only: private
  /clsx/2.1.1:
    clsx: private
  /color-convert/2.0.1:
    color-convert: private
  /color-name/1.1.4:
    color-name: private
  /color-string/1.9.1:
    color-string: private
  /color/4.2.3:
    color: private
  /commander/4.1.1:
    commander: private
  /concat-map/0.0.1:
    concat-map: private
  /cookie/0.7.2:
    cookie: private
  /cross-spawn/7.0.6:
    cross-spawn: private
  /cssesc/3.0.0:
    cssesc: private
  /csstype/3.1.3:
    csstype: private
  /damerau-levenshtein/1.0.8:
    damerau-levenshtein: private
  /data-view-buffer/1.0.2:
    data-view-buffer: private
  /data-view-byte-length/1.0.2:
    data-view-byte-length: private
  /data-view-byte-offset/1.0.1:
    data-view-byte-offset: private
  /debug/4.4.1:
    debug: private
  /deep-is/0.1.4:
    deep-is: private
  /define-data-property/1.1.4:
    define-data-property: private
  /define-properties/1.2.1:
    define-properties: private
  /detect-libc/2.0.4:
    detect-libc: private
  /detect-node-es/1.1.0:
    detect-node-es: private
  /didyoumean/1.2.2:
    didyoumean: private
  /dlv/1.1.3:
    dlv: private
  /doctrine/3.0.0:
    doctrine: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /eastasianwidth/0.2.0:
    eastasianwidth: private
  /electron-to-chromium/1.5.200:
    electron-to-chromium: private
  /emoji-regex/9.2.2:
    emoji-regex: private
  /es-abstract/1.24.0:
    es-abstract: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-iterator-helpers/1.2.1:
    es-iterator-helpers: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /es-set-tostringtag/2.1.0:
    es-set-tostringtag: private
  /es-shim-unscopables/1.1.0:
    es-shim-unscopables: private
  /es-to-primitive/1.3.0:
    es-to-primitive: private
  /escalade/3.2.0:
    escalade: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /eslint-config-next/15.4.6(eslint@8.57.1)(typescript@5.9.2):
    eslint-config-next: public
  /eslint-import-resolver-node/0.3.9:
    eslint-import-resolver-node: public
  /eslint-import-resolver-typescript/3.10.1(eslint-plugin-import@2.32.0)(eslint@8.57.1):
    eslint-import-resolver-typescript: public
  /eslint-module-utils/2.12.1(@typescript-eslint/parser@8.39.1)(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.1)(eslint@8.57.1):
    eslint-module-utils: public
  /eslint-plugin-import/2.32.0(@typescript-eslint/parser@8.39.1)(eslint-import-resolver-typescript@3.10.1)(eslint@8.57.1):
    eslint-plugin-import: public
  /eslint-plugin-jsx-a11y/6.10.2(eslint@8.57.1):
    eslint-plugin-jsx-a11y: public
  /eslint-plugin-react-hooks/5.2.0(eslint@8.57.1):
    eslint-plugin-react-hooks: public
  /eslint-plugin-react/7.37.5(eslint@8.57.1):
    eslint-plugin-react: public
  /eslint-scope/7.2.2:
    eslint-scope: public
  /eslint-visitor-keys/3.4.3:
    eslint-visitor-keys: public
  /eslint/8.57.1:
    eslint: public
  /espree/9.6.1:
    espree: private
  /esquery/1.6.0:
    esquery: private
  /esrecurse/4.3.0:
    esrecurse: private
  /estraverse/5.3.0:
    estraverse: private
  /esutils/2.0.3:
    esutils: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-glob/3.3.3:
    fast-glob: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fast-levenshtein/2.0.6:
    fast-levenshtein: private
  /fastq/1.19.1:
    fastq: private
  /fdir/6.4.6(picomatch@4.0.3):
    fdir: private
  /file-entry-cache/6.0.1:
    file-entry-cache: private
  /fill-range/7.1.1:
    fill-range: private
  /find-up/5.0.0:
    find-up: private
  /flat-cache/3.2.0:
    flat-cache: private
  /flatted/3.3.3:
    flatted: private
  /for-each/0.3.5:
    for-each: private
  /foreground-child/3.3.1:
    foreground-child: private
  /fraction.js/4.3.7:
    fraction.js: private
  /fs.realpath/1.0.0:
    fs.realpath: private
  /fsevents/2.3.3:
    fsevents: private
  /function-bind/1.1.2:
    function-bind: private
  /function.prototype.name/1.1.8:
    function.prototype.name: private
  /functions-have-names/1.2.3:
    functions-have-names: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-nonce/1.0.1:
    get-nonce: private
  /get-proto/1.0.1:
    get-proto: private
  /get-symbol-description/1.1.0:
    get-symbol-description: private
  /get-tsconfig/4.10.1:
    get-tsconfig: private
  /glob-parent/6.0.2:
    glob-parent: private
  /glob/10.4.5:
    glob: private
  /globals/13.24.0:
    globals: private
  /globalthis/1.0.4:
    globalthis: private
  /gopd/1.2.0:
    gopd: private
  /graphemer/1.4.0:
    graphemer: private
  /has-bigints/1.1.0:
    has-bigints: private
  /has-flag/4.0.0:
    has-flag: private
  /has-property-descriptors/1.0.2:
    has-property-descriptors: private
  /has-proto/1.2.0:
    has-proto: private
  /has-symbols/1.1.0:
    has-symbols: private
  /has-tostringtag/1.0.2:
    has-tostringtag: private
  /hasown/2.0.2:
    hasown: private
  /ignore/5.3.2:
    ignore: private
  /import-fresh/3.3.1:
    import-fresh: private
  /imurmurhash/0.1.4:
    imurmurhash: private
  /inflight/1.0.6:
    inflight: private
  /inherits/2.0.4:
    inherits: private
  /internal-slot/1.1.0:
    internal-slot: private
  /is-array-buffer/3.0.5:
    is-array-buffer: private
  /is-arrayish/0.3.2:
    is-arrayish: private
  /is-async-function/2.1.1:
    is-async-function: private
  /is-bigint/1.1.0:
    is-bigint: private
  /is-binary-path/2.1.0:
    is-binary-path: private
  /is-boolean-object/1.2.2:
    is-boolean-object: private
  /is-bun-module/2.0.0:
    is-bun-module: private
  /is-callable/1.2.7:
    is-callable: private
  /is-core-module/2.16.1:
    is-core-module: private
  /is-data-view/1.0.2:
    is-data-view: private
  /is-date-object/1.1.0:
    is-date-object: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-finalizationregistry/1.1.1:
    is-finalizationregistry: private
  /is-fullwidth-code-point/3.0.0:
    is-fullwidth-code-point: private
  /is-generator-function/1.1.0:
    is-generator-function: private
  /is-glob/4.0.3:
    is-glob: private
  /is-map/2.0.3:
    is-map: private
  /is-negative-zero/2.0.3:
    is-negative-zero: private
  /is-number-object/1.1.1:
    is-number-object: private
  /is-number/7.0.0:
    is-number: private
  /is-path-inside/3.0.3:
    is-path-inside: private
  /is-regex/1.2.1:
    is-regex: private
  /is-set/2.0.3:
    is-set: private
  /is-shared-array-buffer/1.0.4:
    is-shared-array-buffer: private
  /is-string/1.1.1:
    is-string: private
  /is-symbol/1.1.1:
    is-symbol: private
  /is-typed-array/1.1.15:
    is-typed-array: private
  /is-weakmap/2.0.2:
    is-weakmap: private
  /is-weakref/1.1.1:
    is-weakref: private
  /is-weakset/2.0.4:
    is-weakset: private
  /isarray/2.0.5:
    isarray: private
  /isexe/2.0.0:
    isexe: private
  /iterator.prototype/1.1.5:
    iterator.prototype: private
  /jackspeak/3.4.3:
    jackspeak: private
  /jiti/1.21.7:
    jiti: private
  /jose/4.15.9:
    jose: private
  /js-tokens/4.0.0:
    js-tokens: private
  /js-yaml/4.1.0:
    js-yaml: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  /json5/1.0.2:
    json5: private
  /jsx-ast-utils/3.3.5:
    jsx-ast-utils: private
  /keyv/4.5.4:
    keyv: private
  /language-subtag-registry/0.3.23:
    language-subtag-registry: private
  /language-tags/1.0.9:
    language-tags: private
  /levn/0.4.1:
    levn: private
  /lilconfig/3.1.3:
    lilconfig: private
  /lines-and-columns/1.2.4:
    lines-and-columns: private
  /locate-path/6.0.0:
    locate-path: private
  /lodash.castarray/4.4.0:
    lodash.castarray: private
  /lodash.isplainobject/4.0.6:
    lodash.isplainobject: private
  /lodash.merge/4.6.2:
    lodash.merge: private
  /loose-envify/1.4.0:
    loose-envify: private
  /lru-cache/6.0.0:
    lru-cache: private
  /lucide-react/0.263.1(react@18.3.1):
    lucide-react: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /merge2/1.4.1:
    merge2: private
  /micromatch/4.0.8:
    micromatch: private
  /mini-svg-data-uri/1.4.4:
    mini-svg-data-uri: private
  /minimatch/3.1.2:
    minimatch: private
  /minimist/1.2.8:
    minimist: private
  /minipass/7.1.2:
    minipass: private
  /ms/2.1.3:
    ms: private
  /mz/2.7.0:
    mz: private
  /nanoid/3.3.11:
    nanoid: private
  /napi-postinstall/0.3.3:
    napi-postinstall: private
  /natural-compare/1.4.0:
    natural-compare: private
  /next-auth/4.24.11(next@15.4.6)(react-dom@18.3.1)(react@18.3.1):
    next-auth: private
  /next-themes/0.4.6(react-dom@18.3.1)(react@18.3.1):
    next-themes: private
  /next/15.4.6(react-dom@18.3.1)(react@18.3.1):
    next: private
  /node-releases/2.0.19:
    node-releases: private
  /normalize-path/3.0.0:
    normalize-path: private
  /normalize-range/0.1.2:
    normalize-range: private
  /oauth/0.9.15:
    oauth: private
  /object-assign/4.1.1:
    object-assign: private
  /object-hash/3.0.0:
    object-hash: private
  /object-inspect/1.13.4:
    object-inspect: private
  /object-keys/1.1.1:
    object-keys: private
  /object.assign/4.1.7:
    object.assign: private
  /object.entries/1.1.9:
    object.entries: private
  /object.fromentries/2.0.8:
    object.fromentries: private
  /object.groupby/1.0.3:
    object.groupby: private
  /object.values/1.2.1:
    object.values: private
  /oidc-token-hash/5.1.1:
    oidc-token-hash: private
  /once/1.4.0:
    once: private
  /openid-client/5.7.1:
    openid-client: private
  /optionator/0.9.4:
    optionator: private
  /own-keys/1.0.1:
    own-keys: private
  /p-limit/3.1.0:
    p-limit: private
  /p-locate/5.0.0:
    p-locate: private
  /package-json-from-dist/1.0.1:
    package-json-from-dist: private
  /parent-module/1.0.1:
    parent-module: private
  /path-exists/4.0.0:
    path-exists: private
  /path-is-absolute/1.0.1:
    path-is-absolute: private
  /path-key/3.1.1:
    path-key: private
  /path-parse/1.0.7:
    path-parse: private
  /path-scurry/1.11.1:
    path-scurry: private
  /picocolors/1.1.1:
    picocolors: private
  /picomatch/2.3.1:
    picomatch: private
  /pify/2.3.0:
    pify: private
  /pirates/4.0.7:
    pirates: private
  /possible-typed-array-names/1.1.0:
    possible-typed-array-names: private
  /postcss-import/15.1.0(postcss@8.5.6):
    postcss-import: private
  /postcss-js/4.0.1(postcss@8.5.6):
    postcss-js: private
  /postcss-load-config/4.0.2(postcss@8.5.6):
    postcss-load-config: private
  /postcss-nested/6.2.0(postcss@8.5.6):
    postcss-nested: private
  /postcss-selector-parser/6.0.10:
    postcss-selector-parser: private
  /postcss-value-parser/4.2.0:
    postcss-value-parser: private
  /postcss/8.5.6:
    postcss: private
  /preact-render-to-string/5.2.6(preact@10.27.0):
    preact-render-to-string: private
  /preact/10.27.0:
    preact: private
  /prelude-ls/1.2.1:
    prelude-ls: private
  /pretty-format/3.8.0:
    pretty-format: private
  /prop-types/15.8.1:
    prop-types: private
  /punycode/2.3.1:
    punycode: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /react-dom/18.3.1(react@18.3.1):
    react-dom: private
  /react-hook-form/7.62.0(react@18.3.1):
    react-hook-form: private
  /react-is/16.13.1:
    react-is: private
  /react-remove-scroll-bar/2.3.8(@types/react@18.3.23)(react@18.3.1):
    react-remove-scroll-bar: private
  /react-remove-scroll/2.7.1(@types/react@18.3.23)(react@18.3.1):
    react-remove-scroll: private
  /react-style-singleton/2.2.3(@types/react@18.3.23)(react@18.3.1):
    react-style-singleton: private
  /react/18.3.1:
    react: private
  /read-cache/1.0.0:
    read-cache: private
  /readdirp/3.6.0:
    readdirp: private
  /reflect.getprototypeof/1.0.10:
    reflect.getprototypeof: private
  /regexp.prototype.flags/1.5.4:
    regexp.prototype.flags: private
  /resolve-from/4.0.0:
    resolve-from: private
  /resolve-pkg-maps/1.0.0:
    resolve-pkg-maps: private
  /resolve/1.22.10:
    resolve: private
  /reusify/1.1.0:
    reusify: private
  /rimraf/3.0.2:
    rimraf: private
  /run-parallel/1.2.0:
    run-parallel: private
  /safe-array-concat/1.1.3:
    safe-array-concat: private
  /safe-push-apply/1.0.0:
    safe-push-apply: private
  /safe-regex-test/1.1.0:
    safe-regex-test: private
  /scheduler/0.23.2:
    scheduler: private
  /semver/6.3.1:
    semver: private
  /set-function-length/1.2.2:
    set-function-length: private
  /set-function-name/2.0.2:
    set-function-name: private
  /set-proto/1.0.0:
    set-proto: private
  /sharp/0.34.3:
    sharp: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /side-channel-list/1.0.0:
    side-channel-list: private
  /side-channel-map/1.0.1:
    side-channel-map: private
  /side-channel-weakmap/1.0.2:
    side-channel-weakmap: private
  /side-channel/1.1.0:
    side-channel: private
  /signal-exit/4.1.0:
    signal-exit: private
  /simple-swizzle/0.2.2:
    simple-swizzle: private
  /source-map-js/1.2.1:
    source-map-js: private
  /stable-hash/0.0.5:
    stable-hash: private
  /stop-iteration-iterator/1.1.0:
    stop-iteration-iterator: private
  /string-width/4.2.3:
    string-width-cjs: private
  /string-width/5.1.2:
    string-width: private
  /string.prototype.includes/2.0.1:
    string.prototype.includes: private
  /string.prototype.matchall/4.0.12:
    string.prototype.matchall: private
  /string.prototype.repeat/1.0.0:
    string.prototype.repeat: private
  /string.prototype.trim/1.2.10:
    string.prototype.trim: private
  /string.prototype.trimend/1.0.9:
    string.prototype.trimend: private
  /string.prototype.trimstart/1.0.8:
    string.prototype.trimstart: private
  /strip-ansi/6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  /strip-bom/3.0.0:
    strip-bom: private
  /strip-json-comments/3.1.1:
    strip-json-comments: private
  /styled-jsx/5.1.6(react@18.3.1):
    styled-jsx: private
  /sucrase/3.35.0:
    sucrase: private
  /supports-color/7.2.0:
    supports-color: private
  /supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  /tailwind-merge/2.6.0:
    tailwind-merge: private
  /tailwindcss-animate/1.0.7(tailwindcss@3.4.17):
    tailwindcss-animate: private
  /tailwindcss/3.4.17:
    tailwindcss: private
  /text-table/0.2.0:
    text-table: private
  /thenify-all/1.6.0:
    thenify-all: private
  /thenify/3.3.1:
    thenify: private
  /tinyglobby/0.2.14:
    tinyglobby: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /ts-api-utils/2.1.0(typescript@5.9.2):
    ts-api-utils: private
  /ts-interface-checker/0.1.13:
    ts-interface-checker: private
  /tsconfig-paths/3.15.0:
    tsconfig-paths: private
  /tslib/2.8.1:
    tslib: private
  /type-check/0.4.0:
    type-check: private
  /type-fest/0.20.2:
    type-fest: private
  /typed-array-buffer/1.0.3:
    typed-array-buffer: private
  /typed-array-byte-length/1.0.3:
    typed-array-byte-length: private
  /typed-array-byte-offset/1.0.4:
    typed-array-byte-offset: private
  /typed-array-length/1.0.7:
    typed-array-length: private
  /unbox-primitive/1.1.0:
    unbox-primitive: private
  /undici-types/6.21.0:
    undici-types: private
  /unrs-resolver/1.11.1:
    unrs-resolver: private
  /update-browserslist-db/1.1.3(browserslist@4.25.2):
    update-browserslist-db: private
  /uri-js/4.4.1:
    uri-js: private
  /use-callback-ref/1.3.3(@types/react@18.3.23)(react@18.3.1):
    use-callback-ref: private
  /use-sidecar/1.1.3(@types/react@18.3.23)(react@18.3.1):
    use-sidecar: private
  /use-sync-external-store/1.5.0(react@18.3.1):
    use-sync-external-store: private
  /util-deprecate/1.0.2:
    util-deprecate: private
  /uuid/8.3.2:
    uuid: private
  /which-boxed-primitive/1.1.1:
    which-boxed-primitive: private
  /which-builtin-type/1.2.1:
    which-builtin-type: private
  /which-collection/1.0.2:
    which-collection: private
  /which-typed-array/1.1.19:
    which-typed-array: private
  /which/2.0.2:
    which: private
  /word-wrap/1.2.5:
    word-wrap: private
  /wrap-ansi/7.0.0:
    wrap-ansi-cjs: private
  /wrap-ansi/8.1.0:
    wrap-ansi: private
  /wrappy/1.0.2:
    wrappy: private
  /yallist/4.0.0:
    yallist: private
  /yaml/2.8.1:
    yaml: private
  /yocto-queue/0.1.0:
    yocto-queue: private
  /zod/3.25.76:
    zod: private
  /zustand/4.5.7(@types/react@18.3.23)(react@18.3.1):
    zustand: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.11.0
pendingBuilds: []
prunedAt: Thu, 14 Aug 2025 03:06:42 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - /@emnapi/core/1.4.5
  - /@emnapi/runtime/1.4.5
  - /@emnapi/wasi-threads/1.0.4
  - /@img/sharp-darwin-x64/0.34.3
  - /@img/sharp-libvips-darwin-x64/1.2.0
  - /@img/sharp-libvips-linux-arm/1.2.0
  - /@img/sharp-libvips-linux-arm64/1.2.0
  - /@img/sharp-libvips-linux-ppc64/1.2.0
  - /@img/sharp-libvips-linux-s390x/1.2.0
  - /@img/sharp-libvips-linux-x64/1.2.0
  - /@img/sharp-libvips-linuxmusl-arm64/1.2.0
  - /@img/sharp-libvips-linuxmusl-x64/1.2.0
  - /@img/sharp-linux-arm/0.34.3
  - /@img/sharp-linux-arm64/0.34.3
  - /@img/sharp-linux-ppc64/0.34.3
  - /@img/sharp-linux-s390x/0.34.3
  - /@img/sharp-linux-x64/0.34.3
  - /@img/sharp-linuxmusl-arm64/0.34.3
  - /@img/sharp-linuxmusl-x64/0.34.3
  - /@img/sharp-wasm32/0.34.3
  - /@img/sharp-win32-arm64/0.34.3
  - /@img/sharp-win32-ia32/0.34.3
  - /@img/sharp-win32-x64/0.34.3
  - /@napi-rs/wasm-runtime/0.2.12
  - /@next/swc-darwin-x64/15.4.6
  - /@next/swc-linux-arm64-gnu/15.4.6
  - /@next/swc-linux-arm64-musl/15.4.6
  - /@next/swc-linux-x64-gnu/15.4.6
  - /@next/swc-linux-x64-musl/15.4.6
  - /@next/swc-win32-arm64-msvc/15.4.6
  - /@next/swc-win32-x64-msvc/15.4.6
  - /@tailwindcss/oxide-android-arm64/4.1.11
  - /@tailwindcss/oxide-darwin-x64/4.1.11
  - /@tailwindcss/oxide-freebsd-x64/4.1.11
  - /@tailwindcss/oxide-linux-arm-gnueabihf/4.1.11
  - /@tailwindcss/oxide-linux-arm64-gnu/4.1.11
  - /@tailwindcss/oxide-linux-arm64-musl/4.1.11
  - /@tailwindcss/oxide-linux-x64-gnu/4.1.11
  - /@tailwindcss/oxide-linux-x64-musl/4.1.11
  - /@tailwindcss/oxide-wasm32-wasi/4.1.11
  - /@tailwindcss/oxide-win32-arm64-msvc/4.1.11
  - /@tailwindcss/oxide-win32-x64-msvc/4.1.11
  - /@tybys/wasm-util/0.10.0
  - /@unrs/resolver-binding-android-arm-eabi/1.11.1
  - /@unrs/resolver-binding-android-arm64/1.11.1
  - /@unrs/resolver-binding-darwin-x64/1.11.1
  - /@unrs/resolver-binding-freebsd-x64/1.11.1
  - /@unrs/resolver-binding-linux-arm-gnueabihf/1.11.1
  - /@unrs/resolver-binding-linux-arm-musleabihf/1.11.1
  - /@unrs/resolver-binding-linux-arm64-gnu/1.11.1
  - /@unrs/resolver-binding-linux-arm64-musl/1.11.1
  - /@unrs/resolver-binding-linux-ppc64-gnu/1.11.1
  - /@unrs/resolver-binding-linux-riscv64-gnu/1.11.1
  - /@unrs/resolver-binding-linux-riscv64-musl/1.11.1
  - /@unrs/resolver-binding-linux-s390x-gnu/1.11.1
  - /@unrs/resolver-binding-linux-x64-gnu/1.11.1
  - /@unrs/resolver-binding-linux-x64-musl/1.11.1
  - /@unrs/resolver-binding-wasm32-wasi/1.11.1
  - /@unrs/resolver-binding-win32-arm64-msvc/1.11.1
  - /@unrs/resolver-binding-win32-ia32-msvc/1.11.1
  - /@unrs/resolver-binding-win32-x64-msvc/1.11.1
  - /lightningcss-darwin-x64/1.30.1
  - /lightningcss-freebsd-x64/1.30.1
  - /lightningcss-linux-arm-gnueabihf/1.30.1
  - /lightningcss-linux-arm64-gnu/1.30.1
  - /lightningcss-linux-arm64-musl/1.30.1
  - /lightningcss-linux-x64-gnu/1.30.1
  - /lightningcss-linux-x64-musl/1.30.1
  - /lightningcss-win32-arm64-msvc/1.30.1
  - /lightningcss-win32-x64-msvc/1.30.1
  - /turbo-darwin-64/2.5.5
  - /turbo-linux-64/2.5.5
  - /turbo-linux-arm64/2.5.5
  - /turbo-windows-64/2.5.5
  - /turbo-windows-arm64/2.5.5
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
