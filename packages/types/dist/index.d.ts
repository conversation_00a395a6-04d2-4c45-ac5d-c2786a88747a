export interface User {
    id: string;
    email: string;
    username: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
    role: UserRole;
    isVerified: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum UserRole {
    BUYER = "buyer",
    SELLER = "seller",
    ADMIN = "admin",
    MODERATOR = "moderator"
}
export interface AuthTokens {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
}
export interface Country {
    id: string;
    name: string;
    code: string;
    states: State[];
}
export interface State {
    id: string;
    name: string;
    code: string;
    countryId: string;
    cities: City[];
}
export interface City {
    id: string;
    name: string;
    stateId: string;
    latitude?: number;
    longitude?: number;
}
export interface Category {
    id: string;
    name: string;
    slug: string;
    description?: string;
    parentId?: string;
    children?: Category[];
    templateIds: string[];
    isActive: boolean;
    sortOrder: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface Template {
    id: string;
    name: string;
    description: string;
    categoryId: string;
    fields: TemplateField[];
    isActive: boolean;
    version: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface TemplateField {
    id: string;
    name: string;
    label: string;
    type: FieldType;
    required: boolean;
    options?: string[];
    validation?: FieldValidation;
    placeholder?: string;
    helpText?: string;
    sortOrder: number;
}
export declare enum FieldType {
    TEXT = "text",
    TEXTAREA = "textarea",
    NUMBER = "number",
    SELECT = "select",
    MULTISELECT = "multiselect",
    CHECKBOX = "checkbox",
    RADIO = "radio",
    DATE = "date",
    EMAIL = "email",
    URL = "url",
    PHONE = "phone",
    CURRENCY = "currency"
}
export interface FieldValidation {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
}
export interface Item {
    id: string;
    title: string;
    description: string;
    price: number;
    currency: string;
    condition: ItemCondition;
    categoryId: string;
    templateId: string;
    sellerId: string;
    locationId: string;
    images: ItemImage[];
    customFields: Record<string, any>;
    status: ItemStatus;
    views: number;
    favorites: number;
    isPromoted: boolean;
    expiresAt?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum ItemCondition {
    NEW = "new",
    LIKE_NEW = "like_new",
    GOOD = "good",
    FAIR = "fair",
    POOR = "poor"
}
export declare enum ItemStatus {
    DRAFT = "draft",
    PENDING_APPROVAL = "pending_approval",
    ACTIVE = "active",
    SOLD = "sold",
    EXPIRED = "expired",
    REJECTED = "rejected",
    REMOVED = "removed"
}
export interface ItemImage {
    id: string;
    url: string;
    thumbnailUrl: string;
    alt?: string;
    sortOrder: number;
    isMain: boolean;
}
export interface SearchQuery {
    q?: string;
    categoryId?: string;
    locationId?: string;
    minPrice?: number;
    maxPrice?: number;
    condition?: ItemCondition[];
    sortBy?: SortOption;
    page?: number;
    limit?: number;
}
export declare enum SortOption {
    RELEVANCE = "relevance",
    NEWEST = "newest",
    OLDEST = "oldest",
    PRICE_LOW_HIGH = "price_low_high",
    PRICE_HIGH_LOW = "price_high_low",
    DISTANCE = "distance"
}
export interface SearchResult {
    items: Item[];
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
    facets: SearchFacets;
}
export interface SearchFacets {
    categories: FacetItem[];
    conditions: FacetItem[];
    priceRanges: FacetItem[];
    locations: FacetItem[];
}
export interface FacetItem {
    value: string;
    label: string;
    count: number;
}
export interface Notification {
    id: string;
    userId: string;
    type: NotificationType;
    title: string;
    message: string;
    data?: Record<string, any>;
    isRead: boolean;
    createdAt: Date;
}
export declare enum NotificationType {
    ITEM_APPROVED = "item_approved",
    ITEM_REJECTED = "item_rejected",
    ITEM_SOLD = "item_sold",
    MESSAGE_RECEIVED = "message_received",
    PRICE_DROP = "price_drop",
    SAVED_SEARCH_MATCH = "saved_search_match"
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: ApiError;
    meta?: ResponseMeta;
}
export interface ApiError {
    code: string;
    message: string;
    details?: Record<string, any>;
}
export interface ResponseMeta {
    page?: number;
    limit?: number;
    total?: number;
    hasMore?: boolean;
}
export interface DomainEvent {
    id: string;
    type: string;
    aggregateId: string;
    aggregateType: string;
    version: number;
    data: Record<string, any>;
    metadata: EventMetadata;
    occurredAt: Date;
}
export interface EventMetadata {
    userId?: string;
    correlationId: string;
    causationId?: string;
    source: string;
}
export type ID = string;
export type Timestamp = Date;
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
