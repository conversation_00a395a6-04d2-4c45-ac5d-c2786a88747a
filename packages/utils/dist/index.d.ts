import { SearchQuery } from '@can-sell/types';
export declare const slugify: (text: string) => string;
export declare const truncate: (text: string, length: number, suffix?: string) => string;
export declare const capitalize: (text: string) => string;
export declare const camelToKebab: (str: string) => string;
export declare const formatCurrency: (amount: number, currency?: string, locale?: string) => string;
export declare const formatNumber: (num: number, locale?: string, options?: Intl.NumberFormatOptions) => string;
export declare const clamp: (value: number, min: number, max: number) => number;
export declare const formatDate: (date: Date | string, locale?: string, options?: Intl.DateTimeFormatOptions) => string;
export declare const formatRelativeTime: (date: Date | string, locale?: string) => string;
export declare const isDateExpired: (date: Date | string) => boolean;
export declare const chunk: <T>(array: T[], size: number) => T[][];
export declare const unique: <T>(array: T[]) => T[];
export declare const groupBy: <T, K extends keyof T>(array: T[], key: K) => Record<string, T[]>;
export declare const pick: <T extends object, K extends keyof T>(obj: T, keys: K[]) => Pick<T, K>;
export declare const omit: <T extends object, K extends keyof T>(obj: T, keys: K[]) => Omit<T, K>;
export declare const isEmpty: (value: any) => boolean;
export declare const isValidEmail: (email: string) => boolean;
export declare const isValidUrl: (url: string) => boolean;
export declare const isValidPhone: (phone: string) => boolean;
export declare const generateId: () => string;
export declare const generateCorrelationId: () => string;
export declare const buildSearchQuery: (params: Partial<SearchQuery>) => SearchQuery;
export declare const sanitizeSearchTerm: (term: string) => string;
export declare const getFileExtension: (filename: string) => string;
export declare const formatFileSize: (bytes: number) => string;
export declare const isImageFile: (filename: string) => boolean;
export declare const sleep: (ms: number) => Promise<void>;
export declare const retry: <T>(fn: () => Promise<T>, maxAttempts?: number, delay?: number) => Promise<T>;
export declare const createError: (message: string, code?: string, details?: any) => Error;
export declare const isErrorWithCode: (error: any, code: string) => boolean;
