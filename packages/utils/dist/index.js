"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isErrorWithCode = exports.createError = exports.retry = exports.sleep = exports.isImageFile = exports.formatFileSize = exports.getFileExtension = exports.sanitizeSearchTerm = exports.buildSearchQuery = exports.generateCorrelationId = exports.generateId = exports.isValidPhone = exports.isValidUrl = exports.isValidEmail = exports.isEmpty = exports.omit = exports.pick = exports.groupBy = exports.unique = exports.chunk = exports.isDateExpired = exports.formatRelativeTime = exports.formatDate = exports.clamp = exports.formatNumber = exports.formatCurrency = exports.camelToKebab = exports.capitalize = exports.truncate = exports.slugify = void 0;
const types_1 = require("@can-sell/types");
// String utilities
const slugify = (text) => {
    return text
        .toLowerCase()
        .trim()
        .replace(/[^\w\s-]/g, '')
        .replace(/[\s_-]+/g, '-')
        .replace(/^-+|-+$/g, '');
};
exports.slugify = slugify;
const truncate = (text, length, suffix = '...') => {
    if (text.length <= length)
        return text;
    return text.substring(0, length - suffix.length) + suffix;
};
exports.truncate = truncate;
const capitalize = (text) => {
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};
exports.capitalize = capitalize;
const camelToKebab = (str) => {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
};
exports.camelToKebab = camelToKebab;
// Number utilities
const formatCurrency = (amount, currency = 'USD', locale = 'en-US') => {
    return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency,
    }).format(amount);
};
exports.formatCurrency = formatCurrency;
const formatNumber = (num, locale = 'en-US', options) => {
    return new Intl.NumberFormat(locale, options).format(num);
};
exports.formatNumber = formatNumber;
const clamp = (value, min, max) => {
    return Math.min(Math.max(value, min), max);
};
exports.clamp = clamp;
// Date utilities
const formatDate = (date, locale = 'en-US', options) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(locale, options).format(dateObj);
};
exports.formatDate = formatDate;
const formatRelativeTime = (date, locale = 'en-US') => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
    if (diffInSeconds < 60)
        return rtf.format(-diffInSeconds, 'second');
    if (diffInSeconds < 3600)
        return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
    if (diffInSeconds < 86400)
        return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
    if (diffInSeconds < 2592000)
        return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
    if (diffInSeconds < 31536000)
        return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
    return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
};
exports.formatRelativeTime = formatRelativeTime;
const isDateExpired = (date) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.getTime() < Date.now();
};
exports.isDateExpired = isDateExpired;
// Array utilities
const chunk = (array, size) => {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
};
exports.chunk = chunk;
const unique = (array) => {
    return [...new Set(array)];
};
exports.unique = unique;
const groupBy = (array, key) => {
    return array.reduce((groups, item) => {
        const group = String(item[key]);
        groups[group] = groups[group] || [];
        groups[group].push(item);
        return groups;
    }, {});
};
exports.groupBy = groupBy;
// Object utilities
const pick = (obj, keys) => {
    const result = {};
    keys.forEach((key) => {
        if (key in obj) {
            result[key] = obj[key];
        }
    });
    return result;
};
exports.pick = pick;
const omit = (obj, keys) => {
    const result = { ...obj };
    keys.forEach((key) => {
        delete result[key];
    });
    return result;
};
exports.omit = omit;
const isEmpty = (value) => {
    if (value == null)
        return true;
    if (typeof value === 'string' || Array.isArray(value))
        return value.length === 0;
    if (typeof value === 'object')
        return Object.keys(value).length === 0;
    return false;
};
exports.isEmpty = isEmpty;
// Validation utilities
const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};
exports.isValidEmail = isValidEmail;
const isValidUrl = (url) => {
    try {
        new URL(url);
        return true;
    }
    catch {
        return false;
    }
};
exports.isValidUrl = isValidUrl;
const isValidPhone = (phone) => {
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
    return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
};
exports.isValidPhone = isValidPhone;
// ID generation
const generateId = () => {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
};
exports.generateId = generateId;
const generateCorrelationId = () => {
    return `${Date.now()}-${Math.random().toString(36).substring(2)}`;
};
exports.generateCorrelationId = generateCorrelationId;
// Search utilities
const buildSearchQuery = (params) => {
    return {
        page: 1,
        limit: 20,
        sortBy: types_1.SortOption.RELEVANCE,
        ...params,
    };
};
exports.buildSearchQuery = buildSearchQuery;
const sanitizeSearchTerm = (term) => {
    return term
        .trim()
        .replace(/[^\w\s]/g, ' ')
        .replace(/\s+/g, ' ')
        .toLowerCase();
};
exports.sanitizeSearchTerm = sanitizeSearchTerm;
// File utilities
const getFileExtension = (filename) => {
    return filename.slice(((filename.lastIndexOf('.') - 1) >>> 0) + 2);
};
exports.getFileExtension = getFileExtension;
const formatFileSize = (bytes) => {
    if (bytes === 0)
        return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
exports.formatFileSize = formatFileSize;
const isImageFile = (filename) => {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    const extension = (0, exports.getFileExtension)(filename).toLowerCase();
    return imageExtensions.includes(extension);
};
exports.isImageFile = isImageFile;
// Async utilities
const sleep = (ms) => {
    return new Promise((resolve) => setTimeout(resolve, ms));
};
exports.sleep = sleep;
const retry = async (fn, maxAttempts = 3, delay = 1000) => {
    let lastError;
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await fn();
        }
        catch (error) {
            lastError = error;
            if (attempt === maxAttempts)
                break;
            await (0, exports.sleep)(delay * attempt);
        }
    }
    throw lastError;
};
exports.retry = retry;
// Error utilities
const createError = (message, code, details) => {
    const error = new Error(message);
    if (code)
        error.code = code;
    if (details)
        error.details = details;
    return error;
};
exports.createError = createError;
const isErrorWithCode = (error, code) => {
    return error && error.code === code;
};
exports.isErrorWithCode = isErrorWithCode;
