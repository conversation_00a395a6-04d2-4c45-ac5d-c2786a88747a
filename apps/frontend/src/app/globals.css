@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Base colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Primary brand colors - Modern blue */
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --primary-50: 214 100% 97%;
    --primary-100: 214 95% 93%;
    --primary-200: 213 97% 87%;
    --primary-300: 212 96% 78%;
    --primary-400: 213 94% 68%;
    --primary-500: 217 91% 60%;
    --primary-600: 221 83% 53%;
    --primary-700: 224 76% 48%;
    --primary-800: 226 71% 40%;
    --primary-900: 224 64% 33%;
    --primary-950: 226 55% 21%;

    /* Secondary colors */
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;

    /* Semantic colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --info: 199 89% 48%;
    --info-foreground: 210 40% 98%;

    /* Neutral colors */
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;

    /* UI elements */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    /* Base colors */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    /* Primary brand colors - Brighter blue for dark mode */
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --primary-50: 226 55% 21%;
    --primary-100: 224 64% 33%;
    --primary-200: 226 71% 40%;
    --primary-300: 224 76% 48%;
    --primary-400: 221 83% 53%;
    --primary-500: 217 91% 60%;
    --primary-600: 213 94% 68%;
    --primary-700: 212 96% 78%;
    --primary-800: 213 97% 87%;
    --primary-900: 214 95% 93%;
    --primary-950: 214 100% 97%;

    /* Secondary colors */
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    /* Semantic colors */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --success: 142 70% 45%;
    --success-foreground: 144 61% 20%;
    --warning: 38 92% 50%;
    --warning-foreground: 20 14.3% 4.1%;
    --info: 199 89% 48%;
    --info-foreground: 210 40% 98%;

    /* Neutral colors */
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    /* UI elements */
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Improved focus styles */
  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-accent-foreground/20;
  }
}

@layer components {
  /* Glass morphism effect */
  .glass {
    @apply bg-background/80 backdrop-blur-md border border-border/50;
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary to-primary-600 bg-clip-text text-transparent;
  }

  /* Shimmer loading effect */
  .shimmer {
    @apply relative overflow-hidden;
  }

  .shimmer::before {
    @apply absolute inset-0 bg-shimmer animate-shimmer;
    content: '';
  }

  /* Enhanced button hover effects */
  .btn-glow {
    @apply transition-all duration-300 hover:shadow-glow;
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }
}
