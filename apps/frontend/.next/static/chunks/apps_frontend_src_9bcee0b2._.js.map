{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n        outline:\n          'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n        secondary:\n          'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-10 px-4 py-2',\n        sm: 'h-9 rounded-md px-3',\n        lg: 'h-11 rounded-md px-8',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : 'button';\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,kQAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,wSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,kSAAC;QACC,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,kQAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,kSAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/components/navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { ThemeToggle } from \"@/components/theme-toggle\";\nimport { Search, User, ShoppingBag, Plus } from \"lucide-react\";\n\nexport function Navigation() {\n  const pathname = usePathname();\n\n  const isActive = (path: string) => pathname === path;\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <ShoppingBag className=\"h-6 w-6\" />\n            <span className=\"font-bold text-xl\">CanSell</span>\n          </Link>\n\n          {/* Search Bar */}\n          <div className=\"flex-1 max-w-md mx-8\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n              <Input\n                placeholder=\"Search for items...\"\n                className=\"pl-10 w-full\"\n              />\n            </div>\n          </div>\n\n          {/* Navigation Links */}\n          <nav className=\"flex items-center space-x-6\">\n            <Link\n              href=\"/browse\"\n              className={`text-sm font-medium transition-colors hover:text-primary ${\n                isActive(\"/browse\") ? \"text-primary\" : \"text-muted-foreground\"\n              }`}\n            >\n              Browse\n            </Link>\n            <Link\n              href=\"/categories\"\n              className={`text-sm font-medium transition-colors hover:text-primary ${\n                isActive(\"/categories\")\n                  ? \"text-primary\"\n                  : \"text-muted-foreground\"\n              }`}\n            >\n              Categories\n            </Link>\n            <Link\n              href=\"/sell\"\n              className={`text-sm font-medium transition-colors hover:text-primary ${\n                isActive(\"/sell\") ? \"text-primary\" : \"text-muted-foreground\"\n              }`}\n            >\n              <Button variant=\"outline\" size=\"sm\">\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Sell\n              </Button>\n            </Link>\n          </nav>\n\n          {/* User Actions */}\n          <div className=\"flex items-center space-x-4\">\n            <Link href=\"/auth/login\">\n              <Button variant=\"ghost\" size=\"sm\">\n                <User className=\"h-4 w-4 mr-2\" />\n                Sign In\n              </Button>\n            </Link>\n            <Link href=\"/auth/register\">\n              <Button size=\"sm\">Sign Up</Button>\n            </Link>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n\nexport function Footer() {\n  return (\n    <footer className=\"border-t bg-background\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div>\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <ShoppingBag className=\"h-5 w-5\" />\n              <span className=\"font-bold\">CanSell</span>\n            </div>\n            <p className=\"text-sm text-muted-foreground\">\n              AI-powered marketplace for buying and selling items with\n              intelligent templates and smart search.\n            </p>\n          </div>\n\n          <div>\n            <h3 className=\"font-semibold mb-4\">Marketplace</h3>\n            <ul className=\"space-y-2 text-sm text-muted-foreground\">\n              <li>\n                <Link href=\"/browse\" className=\"hover:text-primary\">\n                  Browse Items\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/categories\" className=\"hover:text-primary\">\n                  Categories\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/sell\" className=\"hover:text-primary\">\n                  Sell an Item\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/how-it-works\" className=\"hover:text-primary\">\n                  How It Works\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"font-semibold mb-4\">Support</h3>\n            <ul className=\"space-y-2 text-sm text-muted-foreground\">\n              <li>\n                <Link href=\"/help\" className=\"hover:text-primary\">\n                  Help Center\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"hover:text-primary\">\n                  Contact Us\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/safety\" className=\"hover:text-primary\">\n                  Safety Tips\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/community\" className=\"hover:text-primary\">\n                  Community\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2 text-sm text-muted-foreground\">\n              <li>\n                <Link href=\"/about\" className=\"hover:text-primary\">\n                  About Us\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/careers\" className=\"hover:text-primary\">\n                  Careers\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"hover:text-primary\">\n                  Privacy Policy\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"hover:text-primary\">\n                  Terms of Service\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"border-t mt-8 pt-8 text-center text-sm text-muted-foreground\">\n          <p>&copy; 2024 CanSell Ultimate. All rights reserved.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;;;AAPA;;;;;;AASO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,0OAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC,OAAiB,aAAa;IAEhD,qBACE,kSAAC;QAAO,WAAU;kBAChB,cAAA,kSAAC;YAAI,WAAU;sBACb,cAAA,kSAAC;gBAAI,WAAU;;kCAEb,kSAAC,oQAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,kSAAC,4SAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,kSAAC;gCAAK,WAAU;0CAAoB;;;;;;;;;;;;kCAItC,kSAAC;wBAAI,WAAU;kCACb,cAAA,kSAAC;4BAAI,WAAU;;8CACb,kSAAC,8RAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,kSAAC,wJAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,WAAU;;;;;;;;;;;;;;;;;kCAMhB,kSAAC;wBAAI,WAAU;;0CACb,kSAAC,oQAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAW,AAAC,4DAEX,OADC,SAAS,aAAa,iBAAiB;0CAE1C;;;;;;0CAGD,kSAAC,oQAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAW,AAAC,4DAIX,OAHC,SAAS,iBACL,iBACA;0CAEP;;;;;;0CAGD,kSAAC,oQAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAW,AAAC,4DAEX,OADC,SAAS,WAAW,iBAAiB;0CAGvC,cAAA,kSAAC,yJAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,kSAAC,0RAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAOvC,kSAAC;wBAAI,WAAU;;0CACb,kSAAC,oQAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,kSAAC,yJAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;;sDAC3B,kSAAC,0RAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIrC,kSAAC,oQAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,kSAAC,yJAAA,CAAA,SAAM;oCAAC,MAAK;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhC;GA3EgB;;QACG,0OAAA,CAAA,cAAW;;;KADd;AA6ET,SAAS;IACd,qBACE,kSAAC;QAAO,WAAU;kBAChB,cAAA,kSAAC;YAAI,WAAU;;8BACb,kSAAC;oBAAI,WAAU;;sCACb,kSAAC;;8CACC,kSAAC;oCAAI,WAAU;;sDACb,kSAAC,4SAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,kSAAC;4CAAK,WAAU;sDAAY;;;;;;;;;;;;8CAE9B,kSAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAM/C,kSAAC;;8CACC,kSAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,kSAAC;oCAAG,WAAU;;sDACZ,kSAAC;sDACC,cAAA,kSAAC,oQAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAqB;;;;;;;;;;;sDAItD,kSAAC;sDACC,cAAA,kSAAC,oQAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAqB;;;;;;;;;;;sDAI1D,kSAAC;sDACC,cAAA,kSAAC,oQAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqB;;;;;;;;;;;sDAIpD,kSAAC;sDACC,cAAA,kSAAC,oQAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;;sCAOhE,kSAAC;;8CACC,kSAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,kSAAC;oCAAG,WAAU;;sDACZ,kSAAC;sDACC,cAAA,kSAAC,oQAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqB;;;;;;;;;;;sDAIpD,kSAAC;sDACC,cAAA,kSAAC,oQAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAqB;;;;;;;;;;;sDAIvD,kSAAC;sDACC,cAAA,kSAAC,oQAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAqB;;;;;;;;;;;sDAItD,kSAAC;sDACC,cAAA,kSAAC,oQAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;;sCAO7D,kSAAC;;8CACC,kSAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,kSAAC;oCAAG,WAAU;;sDACZ,kSAAC;sDACC,cAAA,kSAAC,oQAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAqB;;;;;;;;;;;sDAIrD,kSAAC;sDACC,cAAA,kSAAC,oQAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAqB;;;;;;;;;;;sDAIvD,kSAAC;sDACC,cAAA,kSAAC,oQAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAqB;;;;;;;;;;;sDAIvD,kSAAC;sDACC,cAAA,kSAAC,oQAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ3D,kSAAC;oBAAI,WAAU;8BACb,cAAA,kSAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb;MArGgB", "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/components/theme-provider.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { ThemeProvider as NextThemesProvider } from 'next-themes';\nimport { type ThemeProviderProps } from 'next-themes/dist/types';\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,KAA0C;QAA1C,EAAE,QAAQ,EAAE,GAAG,OAA2B,GAA1C;IAC5B,qBAAO,kSAAC,iQAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;KAFgB", "debugId": null}}]}