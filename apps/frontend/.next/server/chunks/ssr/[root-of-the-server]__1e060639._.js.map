{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n        outline:\n          'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n        secondary:\n          'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-10 px-4 py-2',\n        sm: 'h-9 rounded-md px-3',\n        lg: 'h-11 rounded-md px-8',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : 'button';\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,0SAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,qSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,mVAAC;QACC,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,0SAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,0SAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,0SAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,0SAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,0SAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,0SAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,0SAAA,CAAA,aAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,mVAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/app/browse/page.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Filter, Grid, List, SlidersHorizontal } from \"lucide-react\";\n\nexport default function BrowsePage() {\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"flex flex-col lg:flex-row gap-8\">\n        {/* Filters Sidebar */}\n        <aside className=\"lg:w-64 space-y-6\">\n          <div>\n            <h3 className=\"font-semibold mb-4 flex items-center\">\n              <SlidersHorizontal className=\"h-4 w-4 mr-2\" />\n              Filters\n            </h3>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"text-sm font-medium mb-2 block\">Price Range</label>\n                <div className=\"flex gap-2\">\n                  <Input placeholder=\"Min\" type=\"number\" />\n                  <Input placeholder=\"Max\" type=\"number\" />\n                </div>\n              </div>\n              \n              <div>\n                <label className=\"text-sm font-medium mb-2 block\">Category</label>\n                <select className=\"w-full p-2 border rounded-md\">\n                  <option>All Categories</option>\n                  <option>Electronics</option>\n                  <option>Vehicles</option>\n                  <option>Home & Garden</option>\n                  <option>Fashion</option>\n                  <option>Sports</option>\n                </select>\n              </div>\n              \n              <div>\n                <label className=\"text-sm font-medium mb-2 block\">Condition</label>\n                <div className=\"space-y-2\">\n                  {['New', 'Like New', 'Good', 'Fair', 'Poor'].map((condition) => (\n                    <label key={condition} className=\"flex items-center\">\n                      <input type=\"checkbox\" className=\"mr-2\" />\n                      <span className=\"text-sm\">{condition}</span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n              \n              <div>\n                <label className=\"text-sm font-medium mb-2 block\">Location</label>\n                <Input placeholder=\"Enter city or zip code\" />\n              </div>\n              \n              <Button className=\"w-full\">\n                <Filter className=\"h-4 w-4 mr-2\" />\n                Apply Filters\n              </Button>\n            </div>\n          </div>\n        </aside>\n\n        {/* Main Content */}\n        <div className=\"flex-1\">\n          {/* Header */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <div>\n              <h1 className=\"text-2xl font-bold\">Browse Items</h1>\n              <p className=\"text-muted-foreground\">Discover amazing items from our community</p>\n            </div>\n            \n            <div className=\"flex items-center gap-4\">\n              <select className=\"p-2 border rounded-md\">\n                <option>Sort by: Newest</option>\n                <option>Sort by: Price (Low to High)</option>\n                <option>Sort by: Price (High to Low)</option>\n                <option>Sort by: Most Popular</option>\n              </select>\n              \n              <div className=\"flex border rounded-md\">\n                <Button variant=\"ghost\" size=\"sm\">\n                  <Grid className=\"h-4 w-4\" />\n                </Button>\n                <Button variant=\"ghost\" size=\"sm\">\n                  <List className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Items Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {/* Sample Items */}\n            {Array.from({ length: 12 }).map((_, i) => (\n              <Card key={i} className=\"overflow-hidden hover:shadow-lg transition-shadow\">\n                <div className=\"aspect-square bg-muted relative\">\n                  <div className=\"absolute inset-0 flex items-center justify-center text-muted-foreground\">\n                    <span>Image Placeholder</span>\n                  </div>\n                </div>\n                <CardHeader className=\"p-4\">\n                  <CardTitle className=\"text-lg\">Sample Item {i + 1}</CardTitle>\n                  <CardDescription>\n                    This is a sample item description that would normally contain details about the product.\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"p-4 pt-0\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-2xl font-bold text-primary\">$99.99</span>\n                    <span className=\"text-sm text-muted-foreground\">Like New</span>\n                  </div>\n                  <div className=\"flex justify-between items-center mt-2\">\n                    <span className=\"text-sm text-muted-foreground\">New York, NY</span>\n                    <Button size=\"sm\">View Details</Button>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          {/* Pagination */}\n          <div className=\"flex justify-center mt-8\">\n            <div className=\"flex gap-2\">\n              <Button variant=\"outline\" disabled>Previous</Button>\n              <Button variant=\"outline\">1</Button>\n              <Button>2</Button>\n              <Button variant=\"outline\">3</Button>\n              <Button variant=\"outline\">Next</Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;AAEe,SAAS;IACtB,qBACE,mVAAC;QAAI,WAAU;kBACb,cAAA,mVAAC;YAAI,WAAU;;8BAEb,mVAAC;oBAAM,WAAU;8BACf,cAAA,mVAAC;;0CACC,mVAAC;gCAAG,WAAU;;kDACZ,mVAAC,qTAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIhD,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;;0DACC,mVAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,mVAAC;gDAAI,WAAU;;kEACb,mVAAC,qJAAA,CAAA,QAAK;wDAAC,aAAY;wDAAM,MAAK;;;;;;kEAC9B,mVAAC,qJAAA,CAAA,QAAK;wDAAC,aAAY;wDAAM,MAAK;;;;;;;;;;;;;;;;;;kDAIlC,mVAAC;;0DACC,mVAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,mVAAC;gDAAO,WAAU;;kEAChB,mVAAC;kEAAO;;;;;;kEACR,mVAAC;kEAAO;;;;;;kEACR,mVAAC;kEAAO;;;;;;kEACR,mVAAC;kEAAO;;;;;;kEACR,mVAAC;kEAAO;;;;;;kEACR,mVAAC;kEAAO;;;;;;;;;;;;;;;;;;kDAIZ,mVAAC;;0DACC,mVAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,mVAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAO;oDAAY;oDAAQ;oDAAQ;iDAAO,CAAC,GAAG,CAAC,CAAC,0BAChD,mVAAC;wDAAsB,WAAU;;0EAC/B,mVAAC;gEAAM,MAAK;gEAAW,WAAU;;;;;;0EACjC,mVAAC;gEAAK,WAAU;0EAAW;;;;;;;uDAFjB;;;;;;;;;;;;;;;;kDAQlB,mVAAC;;0DACC,mVAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,mVAAC,qJAAA,CAAA,QAAK;gDAAC,aAAY;;;;;;;;;;;;kDAGrB,mVAAC,sJAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,mVAAC,2RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;8BAQ3C,mVAAC;oBAAI,WAAU;;sCAEb,mVAAC;4BAAI,WAAU;;8CACb,mVAAC;;sDACC,mVAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,mVAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAO,WAAU;;8DAChB,mVAAC;8DAAO;;;;;;8DACR,mVAAC;8DAAO;;;;;;8DACR,mVAAC;8DAAO;;;;;;8DACR,mVAAC;8DAAO;;;;;;;;;;;;sDAGV,mVAAC;4CAAI,WAAU;;8DACb,mVAAC,sJAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;8DAC3B,cAAA,mVAAC,uRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,mVAAC,sJAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;8DAC3B,cAAA,mVAAC,uRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxB,mVAAC;4BAAI,WAAU;sCAEZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,mVAAC,oJAAA,CAAA,OAAI;oCAAS,WAAU;;sDACtB,mVAAC;4CAAI,WAAU;sDACb,cAAA,mVAAC;gDAAI,WAAU;0DACb,cAAA,mVAAC;8DAAK;;;;;;;;;;;;;;;;sDAGV,mVAAC,oJAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,mVAAC,oJAAA,CAAA,YAAS;oDAAC,WAAU;;wDAAU;wDAAa,IAAI;;;;;;;8DAChD,mVAAC,oJAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,mVAAC,oJAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,mVAAC;oDAAI,WAAU;;sEACb,mVAAC;4DAAK,WAAU;sEAAkC;;;;;;sEAClD,mVAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;;8DAElD,mVAAC;oDAAI,WAAU;;sEACb,mVAAC;4DAAK,WAAU;sEAAgC;;;;;;sEAChD,mVAAC,sJAAA,CAAA,SAAM;4DAAC,MAAK;sEAAK;;;;;;;;;;;;;;;;;;;mCAnBb;;;;;;;;;;sCA2Bf,mVAAC;4BAAI,WAAU;sCACb,cAAA,mVAAC;gCAAI,WAAU;;kDACb,mVAAC,sJAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,QAAQ;kDAAC;;;;;;kDACnC,mVAAC,sJAAA,CAAA,SAAM;wCAAC,SAAQ;kDAAU;;;;;;kDAC1B,mVAAC,sJAAA,CAAA,SAAM;kDAAC;;;;;;kDACR,mVAAC,sJAAA,CAAA,SAAM;wCAAC,SAAQ;kDAAU;;;;;;kDAC1B,mVAAC,sJAAA,CAAA,SAAM;wCAAC,SAAQ;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}]}