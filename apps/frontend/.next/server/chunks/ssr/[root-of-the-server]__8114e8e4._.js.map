{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,0SAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,0SAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,0SAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,0SAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,0SAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,0SAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/app/categories/page.tsx"], "sourcesContent": ["import { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { \n  Smartphone, \n  Car, \n  Home, \n  Shirt, \n  Dumbbell, \n  Book, \n  Music, \n  Gamepad2,\n  Camera,\n  Watch,\n  Sofa,\n  Wrench\n} from \"lucide-react\";\nimport Link from \"next/link\";\n\nconst categories = [\n  {\n    id: 'electronics',\n    name: 'Electronics',\n    description: 'Phones, computers, gadgets and more',\n    icon: Smartphone,\n    itemCount: 1234,\n    subcategories: ['Phones', 'Computers', 'Audio', 'Gaming']\n  },\n  {\n    id: 'vehicles',\n    name: 'Vehicles',\n    description: 'Cars, motorcycles, boats and parts',\n    icon: Car,\n    itemCount: 567,\n    subcategories: ['Cars', 'Motorcycles', 'Boats', 'Parts']\n  },\n  {\n    id: 'home-garden',\n    name: 'Home & Garden',\n    description: 'Furniture, appliances, tools and decor',\n    icon: Home,\n    itemCount: 890,\n    subcategories: ['Furniture', 'Appliances', 'Tools', 'Decor']\n  },\n  {\n    id: 'fashion',\n    name: 'Fashion',\n    description: 'Clothing, shoes, accessories and jewelry',\n    icon: Shirt,\n    itemCount: 2345,\n    subcategories: ['Clothing', 'Shoes', 'Accessories', 'Jewelry']\n  },\n  {\n    id: 'sports',\n    name: 'Sports & Recreation',\n    description: 'Exercise equipment, outdoor gear and sports',\n    icon: Dumbbell,\n    itemCount: 456,\n    subcategories: ['Exercise', 'Outdoor', 'Sports', 'Bikes']\n  },\n  {\n    id: 'books',\n    name: 'Books & Media',\n    description: 'Books, movies, music and educational materials',\n    icon: Book,\n    itemCount: 789,\n    subcategories: ['Books', 'Movies', 'Music', 'Educational']\n  },\n  {\n    id: 'music',\n    name: 'Musical Instruments',\n    description: 'Guitars, keyboards, drums and audio equipment',\n    icon: Music,\n    itemCount: 234,\n    subcategories: ['Guitars', 'Keyboards', 'Drums', 'Audio']\n  },\n  {\n    id: 'gaming',\n    name: 'Gaming',\n    description: 'Video games, consoles and gaming accessories',\n    icon: Gamepad2,\n    itemCount: 678,\n    subcategories: ['Consoles', 'Games', 'Accessories', 'PC Gaming']\n  },\n  {\n    id: 'photography',\n    name: 'Photography',\n    description: 'Cameras, lenses, lighting and accessories',\n    icon: Camera,\n    itemCount: 345,\n    subcategories: ['Cameras', 'Lenses', 'Lighting', 'Accessories']\n  },\n  {\n    id: 'watches',\n    name: 'Watches & Jewelry',\n    description: 'Luxury watches, jewelry and accessories',\n    icon: Watch,\n    itemCount: 123,\n    subcategories: ['Watches', 'Jewelry', 'Accessories', 'Luxury']\n  },\n  {\n    id: 'furniture',\n    name: 'Furniture',\n    description: 'Living room, bedroom, office and outdoor furniture',\n    icon: Sofa,\n    itemCount: 567,\n    subcategories: ['Living Room', 'Bedroom', 'Office', 'Outdoor']\n  },\n  {\n    id: 'tools',\n    name: 'Tools & Hardware',\n    description: 'Power tools, hand tools and hardware supplies',\n    icon: Wrench,\n    itemCount: 432,\n    subcategories: ['Power Tools', 'Hand Tools', 'Hardware', 'Safety']\n  }\n];\n\nexport default function CategoriesPage() {\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"text-center mb-12\">\n        <h1 className=\"text-3xl font-bold mb-4\">Browse by Category</h1>\n        <p className=\"text-xl text-muted-foreground\">\n          Find exactly what you're looking for in our organized categories\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {categories.map((category) => {\n          const IconComponent = category.icon;\n          return (\n            <Link key={category.id} href={`/browse?category=${category.id}`}>\n              <Card className=\"h-full hover:shadow-lg transition-all duration-200 hover:scale-105 cursor-pointer\">\n                <CardHeader className=\"text-center\">\n                  <div className=\"mx-auto mb-4 p-3 bg-primary/10 rounded-full w-fit\">\n                    <IconComponent className=\"h-8 w-8 text-primary\" />\n                  </div>\n                  <CardTitle className=\"text-lg\">{category.name}</CardTitle>\n                  <CardDescription>{category.description}</CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-center mb-4\">\n                    <span className=\"text-2xl font-bold text-primary\">\n                      {category.itemCount.toLocaleString()}\n                    </span>\n                    <span className=\"text-sm text-muted-foreground ml-1\">items</span>\n                  </div>\n                  \n                  <div className=\"space-y-1\">\n                    <p className=\"text-sm font-medium text-muted-foreground mb-2\">Popular:</p>\n                    {category.subcategories.slice(0, 3).map((sub, index) => (\n                      <div key={index} className=\"text-xs text-muted-foreground\">\n                        • {sub}\n                      </div>\n                    ))}\n                    {category.subcategories.length > 3 && (\n                      <div className=\"text-xs text-muted-foreground\">\n                        • +{category.subcategories.length - 3} more\n                      </div>\n                    )}\n                  </div>\n                </CardContent>\n              </Card>\n            </Link>\n          );\n        })}\n      </div>\n\n      {/* Featured Categories */}\n      <div className=\"mt-16\">\n        <h2 className=\"text-2xl font-bold text-center mb-8\">Trending Categories</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <Card className=\"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Smartphone className=\"h-5 w-5 mr-2 text-blue-600\" />\n                Electronics\n              </CardTitle>\n              <CardDescription>\n                Latest gadgets and tech deals\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground\">\n                Discover the newest smartphones, laptops, and smart home devices at great prices.\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-gradient-to-br from-green-50 to-green-100 border-green-200\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Car className=\"h-5 w-5 mr-2 text-green-600\" />\n                Vehicles\n              </CardTitle>\n              <CardDescription>\n                Cars, bikes, and automotive\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground\">\n                Find your next vehicle or sell your current one with our automotive marketplace.\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Shirt className=\"h-5 w-5 mr-2 text-purple-600\" />\n                Fashion\n              </CardTitle>\n              <CardDescription>\n                Style and accessories\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground\">\n                Express your style with fashion items, accessories, and designer pieces.\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;;;AAEA,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,mSAAA,CAAA,aAAU;QAChB,WAAW;QACX,eAAe;YAAC;YAAU;YAAa;YAAS;SAAS;IAC3D;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,qRAAA,CAAA,MAAG;QACT,WAAW;QACX,eAAe;YAAC;YAAQ;YAAe;YAAS;SAAQ;IAC1D;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,uRAAA,CAAA,OAAI;QACV,WAAW;QACX,eAAe;YAAC;YAAa;YAAc;YAAS;SAAQ;IAC9D;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,yRAAA,CAAA,QAAK;QACX,WAAW;QACX,eAAe;YAAC;YAAY;YAAS;YAAe;SAAU;IAChE;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,+RAAA,CAAA,WAAQ;QACd,WAAW;QACX,eAAe;YAAC;YAAY;YAAW;YAAU;SAAQ;IAC3D;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,uRAAA,CAAA,OAAI;QACV,WAAW;QACX,eAAe;YAAC;YAAS;YAAU;YAAS;SAAc;IAC5D;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,yRAAA,CAAA,QAAK;QACX,WAAW;QACX,eAAe;YAAC;YAAW;YAAa;YAAS;SAAQ;IAC3D;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,mSAAA,CAAA,WAAQ;QACd,WAAW;QACX,eAAe;YAAC;YAAY;YAAS;YAAe;SAAY;IAClE;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,2RAAA,CAAA,SAAM;QACZ,WAAW;QACX,eAAe;YAAC;YAAW;YAAU;YAAY;SAAc;IACjE;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,yRAAA,CAAA,QAAK;QACX,WAAW;QACX,eAAe;YAAC;YAAW;YAAW;YAAe;SAAS;IAChE;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,uRAAA,CAAA,OAAI;QACV,WAAW;QACX,eAAe;YAAC;YAAe;YAAW;YAAU;SAAU;IAChE;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,2RAAA,CAAA,SAAM;QACZ,WAAW;QACX,eAAe;YAAC;YAAe;YAAc;YAAY;SAAS;IACpE;CACD;AAEc,SAAS;IACtB,qBACE,mVAAC;QAAI,WAAU;;0BACb,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,mVAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAK/C,mVAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,gBAAgB,SAAS,IAAI;oBACnC,qBACE,mVAAC,iQAAA,CAAA,UAAI;wBAAmB,MAAM,CAAC,iBAAiB,EAAE,SAAS,EAAE,EAAE;kCAC7D,cAAA,mVAAC,oJAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,mVAAC,oJAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,mVAAC;4CAAI,WAAU;sDACb,cAAA,mVAAC;gDAAc,WAAU;;;;;;;;;;;sDAE3B,mVAAC,oJAAA,CAAA,YAAS;4CAAC,WAAU;sDAAW,SAAS,IAAI;;;;;;sDAC7C,mVAAC,oJAAA,CAAA,kBAAe;sDAAE,SAAS,WAAW;;;;;;;;;;;;8CAExC,mVAAC,oJAAA,CAAA,cAAW;;sDACV,mVAAC;4CAAI,WAAU;;8DACb,mVAAC;oDAAK,WAAU;8DACb,SAAS,SAAS,CAAC,cAAc;;;;;;8DAEpC,mVAAC;oDAAK,WAAU;8DAAqC;;;;;;;;;;;;sDAGvD,mVAAC;4CAAI,WAAU;;8DACb,mVAAC;oDAAE,WAAU;8DAAiD;;;;;;gDAC7D,SAAS,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC5C,mVAAC;wDAAgB,WAAU;;4DAAgC;4DACtD;;uDADK;;;;;gDAIX,SAAS,aAAa,CAAC,MAAM,GAAG,mBAC/B,mVAAC;oDAAI,WAAU;;wDAAgC;wDACzC,SAAS,aAAa,CAAC,MAAM,GAAG;wDAAE;;;;;;;;;;;;;;;;;;;;;;;;;uBA1BvC,SAAS,EAAE;;;;;gBAkC1B;;;;;;0BAIF,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,mVAAC;wBAAI,WAAU;;0CACb,mVAAC,oJAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,mVAAC,oJAAA,CAAA,aAAU;;0DACT,mVAAC,oJAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,mVAAC,mSAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAA+B;;;;;;;0DAGvD,mVAAC,oJAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,mVAAC,oJAAA,CAAA,cAAW;kDACV,cAAA,mVAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;0CAMjD,mVAAC,oJAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,mVAAC,oJAAA,CAAA,aAAU;;0DACT,mVAAC,oJAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,mVAAC,qRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;0DAGjD,mVAAC,oJAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,mVAAC,oJAAA,CAAA,cAAW;kDACV,cAAA,mVAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;0CAMjD,mVAAC,oJAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,mVAAC,oJAAA,CAAA,aAAU;;0DACT,mVAAC,oJAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,mVAAC,yRAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;0DAGpD,mVAAC,oJAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,mVAAC,oJAAA,CAAA,cAAW;kDACV,cAAA,mVAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3D", "debugId": null}}]}