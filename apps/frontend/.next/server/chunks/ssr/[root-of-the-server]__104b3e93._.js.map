{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n        outline:\n          'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n        secondary:\n          'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-10 px-4 py-2',\n        sm: 'h-9 rounded-md px-3',\n        lg: 'h-11 rounded-md px-8',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : 'button';\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,0SAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,qSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,mVAAC;QACC,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,0SAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,0SAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,0SAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,0SAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,0SAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,0SAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,0SAAA,CAAA,aAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,mVAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Data/new%20era/CanSell-Ultimate/apps/frontend/src/app/auth/login/page.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { ShoppingBag, Mail, Lock, Eye, EyeOff } from \"lucide-react\";\nimport Link from \"next/link\";\n\nexport default function LoginPage() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-muted/50 px-4\">\n      <div className=\"w-full max-w-md\">\n        <div className=\"text-center mb-8\">\n          <div className=\"flex items-center justify-center mb-4\">\n            <ShoppingBag className=\"h-8 w-8 mr-2\" />\n            <span className=\"text-2xl font-bold\">CanSell</span>\n          </div>\n          <h1 className=\"text-2xl font-bold\">Welcome back</h1>\n          <p className=\"text-muted-foreground\">Sign in to your account to continue</p>\n        </div>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Sign In</CardTitle>\n            <CardDescription>\n              Enter your email and password to access your account\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div>\n              <label className=\"text-sm font-medium mb-2 block\">Email</label>\n              <div className=\"relative\">\n                <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input \n                  type=\"email\" \n                  placeholder=\"Enter your email\" \n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className=\"text-sm font-medium mb-2 block\">Password</label>\n              <div className=\"relative\">\n                <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input \n                  type=\"password\" \n                  placeholder=\"Enter your password\" \n                  className=\"pl-10 pr-10\"\n                />\n                <button className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\n                  <Eye className=\"h-4 w-4 text-muted-foreground\" />\n                </button>\n              </div>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <label className=\"flex items-center\">\n                <input type=\"checkbox\" className=\"mr-2\" />\n                <span className=\"text-sm\">Remember me</span>\n              </label>\n              <Link href=\"/auth/forgot-password\" className=\"text-sm text-primary hover:underline\">\n                Forgot password?\n              </Link>\n            </div>\n\n            <Button className=\"w-full\">\n              Sign In\n            </Button>\n\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <span className=\"w-full border-t\" />\n              </div>\n              <div className=\"relative flex justify-center text-xs uppercase\">\n                <span className=\"bg-background px-2 text-muted-foreground\">\n                  Or continue with\n                </span>\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-2 gap-4\">\n              <Button variant=\"outline\">\n                <svg className=\"h-4 w-4 mr-2\" viewBox=\"0 0 24 24\">\n                  <path fill=\"currentColor\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n                  <path fill=\"currentColor\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n                  <path fill=\"currentColor\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n                  <path fill=\"currentColor\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n                </svg>\n                Google\n              </Button>\n              <Button variant=\"outline\">\n                <svg className=\"h-4 w-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                </svg>\n                Facebook\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n\n        <p className=\"text-center text-sm text-muted-foreground mt-6\">\n          Don't have an account?{\" \"}\n          <Link href=\"/auth/register\" className=\"text-primary hover:underline\">\n            Sign up\n          </Link>\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;;AAEe,SAAS;IACtB,qBACE,mVAAC;QAAI,WAAU;kBACb,cAAA,mVAAC;YAAI,WAAU;;8BACb,mVAAC;oBAAI,WAAU;;sCACb,mVAAC;4BAAI,WAAU;;8CACb,mVAAC,ySAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,mVAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;;sCAEvC,mVAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,mVAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAGvC,mVAAC,oJAAA,CAAA,OAAI;;sCACH,mVAAC,oJAAA,CAAA,aAAU;;8CACT,mVAAC,oJAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,mVAAC,oJAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,mVAAC,oJAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,mVAAC;;sDACC,mVAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,mVAAC;4CAAI,WAAU;;8DACb,mVAAC,uRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,mVAAC,qJAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,mVAAC;;sDACC,mVAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,mVAAC;4CAAI,WAAU;;8DACb,mVAAC,uRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,mVAAC,qJAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;8DAEZ,mVAAC;oDAAO,WAAU;8DAChB,cAAA,mVAAC,qRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAKrB,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAM,WAAU;;8DACf,mVAAC;oDAAM,MAAK;oDAAW,WAAU;;;;;;8DACjC,mVAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,mVAAC,iQAAA,CAAA,UAAI;4CAAC,MAAK;4CAAwB,WAAU;sDAAuC;;;;;;;;;;;;8CAKtF,mVAAC,sJAAA,CAAA,SAAM;oCAAC,WAAU;8CAAS;;;;;;8CAI3B,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;sDACb,cAAA,mVAAC;gDAAK,WAAU;;;;;;;;;;;sDAElB,mVAAC;4CAAI,WAAU;sDACb,cAAA,mVAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;;;;;;8CAM/D,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,sJAAA,CAAA,SAAM;4CAAC,SAAQ;;8DACd,mVAAC;oDAAI,WAAU;oDAAe,SAAQ;;sEACpC,mVAAC;4DAAK,MAAK;4DAAe,GAAE;;;;;;sEAC5B,mVAAC;4DAAK,MAAK;4DAAe,GAAE;;;;;;sEAC5B,mVAAC;4DAAK,MAAK;4DAAe,GAAE;;;;;;sEAC5B,mVAAC;4DAAK,MAAK;4DAAe,GAAE;;;;;;;;;;;;gDACxB;;;;;;;sDAGR,mVAAC,sJAAA,CAAA,SAAM;4CAAC,SAAQ;;8DACd,mVAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAe,SAAQ;8DACxD,cAAA,mVAAC;wDAAK,GAAE;;;;;;;;;;;gDACJ;;;;;;;;;;;;;;;;;;;;;;;;;8BAOd,mVAAC;oBAAE,WAAU;;wBAAiD;wBACrC;sCACvB,mVAAC,iQAAA,CAAA,UAAI;4BAAC,MAAK;4BAAiB,WAAU;sCAA+B;;;;;;;;;;;;;;;;;;;;;;;AAO/E", "debugId": null}}]}