const CHUNK_PUBLIC_PATH = "server/app/page.js";
const runtime = require("../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_83761aae._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__d8593af1._.js");
runtime.loadChunk("server/chunks/ssr/apps_frontend_src_app_a0a627ec._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__b98c57f2._.js");
runtime.loadChunk("server/chunks/ssr/2c0cf_next_dist_client_components_99eac24e._.js");
runtime.loadChunk("server/chunks/ssr/2c0cf_next_dist_client_components_builtin_forbidden_db0e6e03.js");
runtime.loadChunk("server/chunks/ssr/2c0cf_next_dist_client_components_builtin_unauthorized_b56beb4c.js");
runtime.loadChunk("server/chunks/ssr/2c0cf_next_dist_client_components_builtin_global-error_669a42db.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_3150ac4d._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__f052159e._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/frontend/.next-internal/server/app/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.4.6_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/node_modules/.pnpm/next@15.4.6_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/apps/frontend/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/apps/frontend/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/apps/frontend/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.4.6_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.4.6_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.4.6_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/node_modules/.pnpm/next@15.4.6_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/apps/frontend/src/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.4.6_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/node_modules/.pnpm/next@15.4.6_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/apps/frontend/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/apps/frontend/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/apps/frontend/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.4.6_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.4.6_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.4.6_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/node_modules/.pnpm/next@15.4.6_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/apps/frontend/src/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
